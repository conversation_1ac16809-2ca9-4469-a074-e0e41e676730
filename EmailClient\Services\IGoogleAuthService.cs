using Google.Apis.Auth.OAuth2;
using EmailClient.Models;

namespace EmailClient.Services;

public interface IGoogleAuthService
{
    /// <summary>
    /// Initiates the OAuth2 authentication flow
    /// </summary>
    /// <param name="clientId">Google OAuth2 client ID</param>
    /// <param name="clientSecret">Google OAuth2 client secret</param>
    /// <param name="scopes">Requested OAuth2 scopes</param>
    /// <returns>True if authentication was successful</returns>
    Task<bool> AuthenticateAsync(string clientId, string clientSecret, string[] scopes);
    
    /// <summary>
    /// Gets the current authentication status
    /// </summary>
    /// <returns>True if currently authenticated</returns>
    Task<bool> IsAuthenticatedAsync();
    
    /// <summary>
    /// Gets the current user credential for API calls
    /// </summary>
    /// <returns>UserCredential if authenticated, null otherwise</returns>
    Task<UserCredential?> GetCredentialAsync();
    
    /// <summary>
    /// Refreshes the access token if needed
    /// </summary>
    /// <returns>True if token was refreshed successfully</returns>
    Task<bool> RefreshTokenAsync();
    
    /// <summary>
    /// Revokes the current authentication and clears stored tokens
    /// </summary>
    Task RevokeAuthenticationAsync();
    
    /// <summary>
    /// Gets the current Google API settings
    /// </summary>
    /// <returns>GoogleApiSettings if configured, null otherwise</returns>
    Task<GoogleApiSettings?> GetSettingsAsync();
    
    /// <summary>
    /// Updates the Google API settings
    /// </summary>
    /// <param name="settings">Updated settings</param>
    Task UpdateSettingsAsync(GoogleApiSettings settings);
    
    /// <summary>
    /// Gets the authenticated user's profile information
    /// </summary>
    /// <returns>User profile information</returns>
    Task<GoogleUserProfile?> GetUserProfileAsync();
}

public class GoogleUserProfile
{
    public string Id { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string GivenName { get; set; } = string.Empty;
    public string FamilyName { get; set; } = string.Empty;
    public string Picture { get; set; } = string.Empty;
    public bool VerifiedEmail { get; set; } = false;
}
