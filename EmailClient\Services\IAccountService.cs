using EmailClient.Models;

namespace EmailClient.Services;

public interface IAccountService
{
    Task<IEnumerable<EmailAccount>> GetAllAccountsAsync();
    Task<EmailAccount?> GetAccountAsync(int id);
    Task<EmailAccount> CreateAccountAsync(EmailAccount account);
    Task<EmailAccount> UpdateAccountAsync(EmailAccount account);
    Task DeleteAccountAsync(int id);
    Task<bool> TestAccountConnectionAsync(EmailAccount account);
    Task<IEnumerable<EmailFolder>> GetAccountFoldersAsync(int accountId);
}
