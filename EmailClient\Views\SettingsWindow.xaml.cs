using EmailClient.ViewModels;
using System.Windows;

namespace EmailClient.Views;

public partial class SettingsWindow : Window
{
    private SettingsViewModel _viewModel;

    public SettingsWindow(SettingsViewModel viewModel)
    {
        InitializeComponent();
        _viewModel = viewModel;
        DataContext = viewModel;

        // Set up PasswordBox handling
        GoogleClientSecretBox.PasswordChanged += GoogleClientSecretBox_PasswordChanged;

        // Load existing password if available
        if (!string.IsNullOrEmpty(viewModel.GoogleClientSecret))
        {
            GoogleClientSecretBox.Password = viewModel.GoogleClientSecret;
        }
    }

    private void GoogleClientSecretBox_PasswordChanged(object sender, RoutedEventArgs e)
    {
        if (DataContext is SettingsViewModel viewModel)
        {
            viewModel.GoogleClientSecret = GoogleClientSecretBox.Password;
        }
    }

    protected override void OnClosed(EventArgs e)
    {
        // Clean up event handler
        GoogleClientSecretBox.PasswordChanged -= GoogleClientSecretBox_PasswordChanged;
        base.OnClosed(e);
    }
}
