using Google.Apis.PeopleService.v1;
using Google.Apis.PeopleService.v1.Data;
using Google.Apis.Services;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using EmailClient.Data;
using EmailClient.Models;
using System.Text.Json;

namespace EmailClient.Services;

public class GoogleContactsService : IGoogleContactsService
{
    private readonly EmailDbContext _context;
    private readonly IGoogleAuthService _authService;
    private readonly ILogger<GoogleContactsService> _logger;

    public GoogleContactsService(
        EmailDbContext context, 
        IGoogleAuthService authService, 
        ILogger<GoogleContactsService> logger)
    {
        _context = context;
        _authService = authService;
        _logger = logger;
    }

    public async Task<int> SyncContactsAsync(bool fullSync = false)
    {
        try
        {
            if (!await IsSyncEnabledAsync())
            {
                _logger.LogInformation("Contacts sync is disabled");
                return 0;
            }

            var credential = await _authService.GetCredentialAsync();
            if (credential == null)
            {
                _logger.LogWarning("No Google authentication available for contacts sync");
                return 0;
            }

            var service = new PeopleServiceService(new BaseClientService.Initializer()
            {
                HttpClientInitializer = credential,
                ApplicationName = "Email Client"
            });

            var syncedCount = 0;
            var settings = await _authService.GetSettingsAsync();
            
            if (fullSync || string.IsNullOrEmpty(settings?.ContactsSyncToken))
            {
                syncedCount = await PerformFullSyncAsync(service);
            }
            else
            {
                syncedCount = await PerformIncrementalSyncAsync(service, settings.ContactsSyncToken);
            }

            // Update last sync time
            if (settings != null)
            {
                settings.LastContactsSync = DateTime.UtcNow;
                await _context.SaveChangesAsync();
            }

            _logger.LogInformation("Synced {Count} contacts", syncedCount);
            return syncedCount;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to sync contacts");
            return 0;
        }
    }

    public async Task<IEnumerable<Contact>> GetContactsAsync()
    {
        return await _context.Contacts
            .Where(c => !c.IsDeleted)
            .OrderBy(c => c.DisplayName)
            .ToListAsync();
    }

    public async Task<Contact?> GetContactAsync(int id)
    {
        return await _context.Contacts
            .FirstOrDefaultAsync(c => c.Id == id && !c.IsDeleted);
    }

    public async Task<Contact?> GetContactByGoogleIdAsync(string googleId)
    {
        return await _context.Contacts
            .FirstOrDefaultAsync(c => c.GoogleId == googleId && !c.IsDeleted);
    }

    public async Task<IEnumerable<Contact>> SearchContactsAsync(string query)
    {
        var lowerQuery = query.ToLower();
        return await _context.Contacts
            .Where(c => !c.IsDeleted && 
                (c.DisplayName.ToLower().Contains(lowerQuery) ||
                 c.GivenName.ToLower().Contains(lowerQuery) ||
                 c.FamilyName.ToLower().Contains(lowerQuery) ||
                 c.EmailAddresses.ToLower().Contains(lowerQuery)))
            .OrderBy(c => c.DisplayName)
            .ToListAsync();
    }

    public async Task<Contact?> CreateContactAsync(Contact contact)
    {
        try
        {
            var credential = await _authService.GetCredentialAsync();
            if (credential == null)
                return null;

            var service = new PeopleServiceService(new BaseClientService.Initializer()
            {
                HttpClientInitializer = credential,
                ApplicationName = "Email Client"
            });

            var person = ConvertToGooglePerson(contact);
            var request = service.People.CreateContact(person);
            var createdPerson = await request.ExecuteAsync();

            if (createdPerson?.ResourceName != null)
            {
                contact.GoogleId = createdPerson.ResourceName;
                contact.ETag = createdPerson.Etag ?? string.Empty;
                contact.CreatedAt = DateTime.UtcNow;
                contact.UpdatedAt = DateTime.UtcNow;
                contact.LastSyncAt = DateTime.UtcNow;

                _context.Contacts.Add(contact);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Created contact: {Name}", contact.DisplayName);
                return contact;
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create contact: {Name}", contact.DisplayName);
            return null;
        }
    }

    public async Task<Contact?> UpdateContactAsync(Contact contact)
    {
        try
        {
            var credential = await _authService.GetCredentialAsync();
            if (credential == null)
                return null;

            var service = new PeopleServiceService(new BaseClientService.Initializer()
            {
                HttpClientInitializer = credential,
                ApplicationName = "Email Client"
            });

            var person = ConvertToGooglePerson(contact);
            person.Etag = contact.ETag;

            var request = service.People.UpdateContact(person, contact.GoogleId);
            request.UpdatePersonFields = "names,emailAddresses,phoneNumbers,addresses,organizations,birthdays,biographies";
            
            var updatedPerson = await request.ExecuteAsync();

            if (updatedPerson != null)
            {
                contact.ETag = updatedPerson.Etag ?? string.Empty;
                contact.UpdatedAt = DateTime.UtcNow;
                contact.LastSyncAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                _logger.LogInformation("Updated contact: {Name}", contact.DisplayName);
                return contact;
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update contact: {Name}", contact.DisplayName);
            return null;
        }
    }

    public async Task<bool> DeleteContactAsync(int id)
    {
        try
        {
            var contact = await GetContactAsync(id);
            if (contact == null)
                return false;

            var credential = await _authService.GetCredentialAsync();
            if (credential != null)
            {
                var service = new PeopleServiceService(new BaseClientService.Initializer()
                {
                    HttpClientInitializer = credential,
                    ApplicationName = "Email Client"
                });

                var request = service.People.DeleteContact(contact.GoogleId);
                await request.ExecuteAsync();
            }

            // Mark as deleted locally
            contact.IsDeleted = true;
            contact.UpdatedAt = DateTime.UtcNow;
            await _context.SaveChangesAsync();

            _logger.LogInformation("Deleted contact: {Name}", contact.DisplayName);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to delete contact with ID: {Id}", id);
            return false;
        }
    }

    public async Task<IEnumerable<Contact>> GetUpdatedContactsAsync(DateTime since)
    {
        return await _context.Contacts
            .Where(c => !c.IsDeleted && c.UpdatedAt > since)
            .OrderBy(c => c.UpdatedAt)
            .ToListAsync();
    }

    public async Task<DateTime?> GetLastSyncTimeAsync()
    {
        var settings = await _authService.GetSettingsAsync();
        return settings?.LastContactsSync;
    }

    public async Task<bool> IsSyncEnabledAsync()
    {
        var settings = await _authService.GetSettingsAsync();
        return settings?.ContactsSyncEnabled == true && await _authService.IsAuthenticatedAsync();
    }

    private async Task<int> PerformFullSyncAsync(PeopleServiceService service)
    {
        var request = service.People.Connections.List("people/me");
        request.PersonFields = "names,emailAddresses,phoneNumbers,addresses,organizations,birthdays,biographies,photos,metadata";
        request.PageSize = 1000;

        var syncedCount = 0;
        string? nextPageToken = null;

        do
        {
            if (!string.IsNullOrEmpty(nextPageToken))
                request.PageToken = nextPageToken;

            var response = await request.ExecuteAsync();
            
            if (response.Connections != null)
            {
                foreach (var person in response.Connections)
                {
                    await ProcessPersonAsync(person);
                    syncedCount++;
                }
            }

            nextPageToken = response.NextPageToken;
        } while (!string.IsNullOrEmpty(nextPageToken));

        await _context.SaveChangesAsync();
        return syncedCount;
    }

    private async Task<int> PerformIncrementalSyncAsync(PeopleServiceService service, string syncToken)
    {
        // Note: Google People API doesn't support sync tokens like Calendar API
        // For now, we'll perform a full sync and compare timestamps
        // In a production app, you might want to implement a more sophisticated approach
        return await PerformFullSyncAsync(service);
    }

    private async Task ProcessPersonAsync(Person person)
    {
        if (person.ResourceName == null)
            return;

        var existingContact = await GetContactByGoogleIdAsync(person.ResourceName);
        var contact = existingContact ?? new Contact();

        // Update contact from Google person data
        UpdateContactFromPerson(contact, person);

        if (existingContact == null)
        {
            _context.Contacts.Add(contact);
        }
        else
        {
            existingContact.UpdatedAt = DateTime.UtcNow;
            existingContact.LastSyncAt = DateTime.UtcNow;
        }
    }

    private void UpdateContactFromPerson(Contact contact, Person person)
    {
        contact.GoogleId = person.ResourceName ?? string.Empty;
        contact.ETag = person.Etag ?? string.Empty;
        
        // Names
        var name = person.Names?.FirstOrDefault();
        if (name != null)
        {
            contact.DisplayName = name.DisplayName ?? string.Empty;
            contact.GivenName = name.GivenName ?? string.Empty;
            contact.FamilyName = name.FamilyName ?? string.Empty;
            contact.MiddleName = name.MiddleName ?? string.Empty;
        }

        // Email addresses
        if (person.EmailAddresses != null)
        {
            var emails = person.EmailAddresses.Select(e => new ContactEmail
            {
                Value = e.Value ?? string.Empty,
                Type = e.Type ?? string.Empty,
                Primary = e.Metadata?.Primary == true
            });
            contact.EmailAddresses = JsonSerializer.Serialize(emails);
        }

        // Phone numbers
        if (person.PhoneNumbers != null)
        {
            var phones = person.PhoneNumbers.Select(p => new ContactPhone
            {
                Value = p.Value ?? string.Empty,
                Type = p.Type ?? string.Empty,
                Primary = p.Metadata?.Primary == true
            });
            contact.PhoneNumbers = JsonSerializer.Serialize(phones);
        }

        // Addresses
        if (person.Addresses != null)
        {
            var addresses = person.Addresses.Select(a => new ContactAddress
            {
                FormattedValue = a.FormattedValue ?? string.Empty,
                StreetAddress = a.StreetAddress ?? string.Empty,
                City = a.City ?? string.Empty,
                Region = a.Region ?? string.Empty,
                PostalCode = a.PostalCode ?? string.Empty,
                Country = a.Country ?? string.Empty,
                Type = a.Type ?? string.Empty
            });
            contact.Addresses = JsonSerializer.Serialize(addresses);
        }

        // Organizations
        if (person.Organizations != null)
        {
            var orgs = person.Organizations.Select(o => new ContactOrganization
            {
                Name = o.Name ?? string.Empty,
                Title = o.Title ?? string.Empty,
                Department = o.Department ?? string.Empty,
                Type = o.Type ?? string.Empty
            });
            contact.Organizations = JsonSerializer.Serialize(orgs);
        }

        // Photo
        var photo = person.Photos?.FirstOrDefault();
        if (photo != null)
        {
            contact.PhotoUrl = photo.Url ?? string.Empty;
        }

        // Birthday
        var birthday = person.Birthdays?.FirstOrDefault();
        if (birthday?.Date != null)
        {
            try
            {
                var date = birthday.Date;
                if (date.Year.HasValue && date.Month.HasValue && date.Day.HasValue)
                {
                    contact.Birthday = new DateTime(date.Year.Value, date.Month.Value, date.Day.Value);
                }
            }
            catch
            {
                // Ignore invalid dates
            }
        }

        // Notes/Biography
        var bio = person.Biographies?.FirstOrDefault();
        if (bio != null)
        {
            contact.Notes = bio.Value ?? string.Empty;
        }

        contact.LastSyncAt = DateTime.UtcNow;
        if (contact.Id == 0)
        {
            contact.CreatedAt = DateTime.UtcNow;
        }
        contact.UpdatedAt = DateTime.UtcNow;
    }

    private Person ConvertToGooglePerson(Contact contact)
    {
        var person = new Person();

        // Names
        if (!string.IsNullOrEmpty(contact.DisplayName) || !string.IsNullOrEmpty(contact.GivenName) || !string.IsNullOrEmpty(contact.FamilyName))
        {
            person.Names = new List<Name>
            {
                new Name
                {
                    DisplayName = contact.DisplayName,
                    GivenName = contact.GivenName,
                    FamilyName = contact.FamilyName,
                    MiddleName = contact.MiddleName
                }
            };
        }

        // Email addresses
        if (!string.IsNullOrEmpty(contact.EmailAddresses))
        {
            try
            {
                var emails = JsonSerializer.Deserialize<ContactEmail[]>(contact.EmailAddresses);
                if (emails != null)
                {
                    person.EmailAddresses = emails.Select(e => new EmailAddress
                    {
                        Value = e.Value,
                        Type = e.Type
                    }).ToList();
                }
            }
            catch
            {
                // Ignore JSON parsing errors
            }
        }

        // Phone numbers
        if (!string.IsNullOrEmpty(contact.PhoneNumbers))
        {
            try
            {
                var phones = JsonSerializer.Deserialize<ContactPhone[]>(contact.PhoneNumbers);
                if (phones != null)
                {
                    person.PhoneNumbers = phones.Select(p => new PhoneNumber
                    {
                        Value = p.Value,
                        Type = p.Type
                    }).ToList();
                }
            }
            catch
            {
                // Ignore JSON parsing errors
            }
        }

        // Notes/Biography
        if (!string.IsNullOrEmpty(contact.Notes))
        {
            person.Biographies = new List<Biography>
            {
                new Biography { Value = contact.Notes }
            };
        }

        return person;
    }
}
