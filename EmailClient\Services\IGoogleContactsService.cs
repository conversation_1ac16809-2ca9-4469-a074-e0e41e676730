using EmailClient.Models;

namespace EmailClient.Services;

public interface IGoogleContactsService
{
    /// <summary>
    /// Synchronizes contacts from Google People API
    /// </summary>
    /// <param name="fullSync">If true, performs full sync; if false, performs incremental sync</param>
    /// <returns>Number of contacts synchronized</returns>
    Task<int> SyncContactsAsync(bool fullSync = false);
    
    /// <summary>
    /// Gets all contacts from local database
    /// </summary>
    /// <returns>List of contacts</returns>
    Task<IEnumerable<Contact>> GetContactsAsync();
    
    /// <summary>
    /// Gets a specific contact by ID
    /// </summary>
    /// <param name="id">Contact ID</param>
    /// <returns>Contact if found, null otherwise</returns>
    Task<Contact?> GetContactAsync(int id);
    
    /// <summary>
    /// Gets a contact by Google ID
    /// </summary>
    /// <param name="googleId">Google resource name</param>
    /// <returns>Contact if found, null otherwise</returns>
    Task<Contact?> GetContactByGoogleIdAsync(string googleId);
    
    /// <summary>
    /// Searches contacts by name or email
    /// </summary>
    /// <param name="query">Search query</param>
    /// <returns>Matching contacts</returns>
    Task<IEnumerable<Contact>> SearchContactsAsync(string query);
    
    /// <summary>
    /// Creates a new contact in Google and syncs to local database
    /// </summary>
    /// <param name="contact">Contact to create</param>
    /// <returns>Created contact with Google ID</returns>
    Task<Contact?> CreateContactAsync(Contact contact);
    
    /// <summary>
    /// Updates an existing contact in Google and local database
    /// </summary>
    /// <param name="contact">Contact to update</param>
    /// <returns>Updated contact</returns>
    Task<Contact?> UpdateContactAsync(Contact contact);
    
    /// <summary>
    /// Deletes a contact from Google and local database
    /// </summary>
    /// <param name="id">Contact ID</param>
    /// <returns>True if deleted successfully</returns>
    Task<bool> DeleteContactAsync(int id);
    
    /// <summary>
    /// Gets contacts that have been updated since the last sync
    /// </summary>
    /// <param name="since">Date to check for updates since</param>
    /// <returns>Updated contacts</returns>
    Task<IEnumerable<Contact>> GetUpdatedContactsAsync(DateTime since);
    
    /// <summary>
    /// Gets the last sync timestamp
    /// </summary>
    /// <returns>Last sync timestamp, null if never synced</returns>
    Task<DateTime?> GetLastSyncTimeAsync();
    
    /// <summary>
    /// Checks if contacts sync is enabled
    /// </summary>
    /// <returns>True if enabled</returns>
    Task<bool> IsSyncEnabledAsync();
}
