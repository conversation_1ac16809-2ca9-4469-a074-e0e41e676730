# Email Client - Windows IMAP Email Application

A native Windows desktop application for managing multiple IMAP email accounts with a unified inbox interface.

## Features

- **Multi-Account Support**: Connect multiple IMAP email accounts
- **Unified Inbox**: View emails from all accounts in one place
- **Folder Management**: Browse and organize emails by folders
- **Email Operations**: Read, delete, mark as read/unread, search
- **Modern UI**: Clean WPF interface with three-pane layout
- **Local Storage**: SQLite database for offline email access
- **Real-time Sync**: Background synchronization with IMAP servers

## Technology Stack

- **.NET 8** - Modern .NET framework
- **WPF** - Windows Presentation Foundation for native UI
- **MailKit** - Robust IMAP/SMTP library
- **Entity Framework Core** - Database ORM with SQLite
- **MVVM Pattern** - Clean separation of concerns
- **Dependency Injection** - Microsoft.Extensions.DependencyInjection

## Prerequisites

1. **Install .NET 8 SDK**
   - Download from: https://dotnet.microsoft.com/download/dotnet/8.0
   - Choose the SDK (not just runtime)
   - Restart your terminal after installation

2. **Windows 10/11** - Required for WPF applications

## Setup Instructions

1. **Clone or download the project**
   ```bash
   cd "c:\Users\<USER>\dev\windows app"
   ```

2. **Restore NuGet packages**
   ```bash
   cd EmailClient
   dotnet restore
   ```

3. **Build the application**
   ```bash
   dotnet build
   ```

4. **Run the application**
   ```bash
   dotnet run
   ```

## Project Structure

```
EmailClient/
├── Models/                 # Data models (EmailAccount, EmailMessage, etc.)
├── Data/                   # Entity Framework DbContext
├── Services/              # Business logic and IMAP operations
├── ViewModels/            # MVVM ViewModels
├── Views/                 # WPF Windows and UserControls
├── Styles/                # XAML styling resources
└── App.xaml              # Application entry point
```

## Key Components

### Models
- `EmailAccount` - IMAP account configuration
- `EmailMessage` - Email message data
- `EmailFolder` - IMAP folder structure
- `EmailAttachment` - File attachments

### Services
- `ImapService` - Core IMAP operations using MailKit
- `EmailService` - High-level email management
- `AccountService` - Account management operations
- `SettingsService` - Application settings persistence

### ViewModels
- `MainWindowViewModel` - Main application logic
- `FolderTreeItemViewModel` - Folder tree display
- `EmailMessageViewModel` - Email list display

## Current Status

✅ **Completed:**
- Project structure and architecture
- Data models and Entity Framework setup
- Core IMAP service implementation
- Basic WPF UI with three-pane layout
- Dependency injection configuration
- Logging and error handling framework

🚧 **In Progress:**
- Account setup UI
- Email synchronization
- Message display and operations

📋 **Planned:**
- Account configuration dialog
- Email composition and sending
- Advanced search functionality
- Settings and preferences
- Performance optimizations

## Adding Email Accounts

Currently, email accounts need to be added programmatically. The account setup UI is planned for the next development phase.

Example account configuration:
```csharp
var account = new EmailAccount
{
    Name = "My Gmail",
    EmailAddress = "<EMAIL>",
    ImapServer = "imap.gmail.com",
    ImapPort = 993,
    UseSsl = true,
    Username = "<EMAIL>",
    Password = "app-password" // Use app-specific password for Gmail
};
```

## Supported Email Providers

The application works with any IMAP-compatible email provider:
- Gmail (imap.gmail.com:993)
- Outlook/Hotmail (outlook.office365.com:993)
- Yahoo Mail (imap.mail.yahoo.com:993)
- Custom IMAP servers

## Security Notes

- Passwords are currently stored in plain text in the local database
- For production use, implement proper encryption for stored credentials
- Use app-specific passwords for providers that support them (Gmail, Outlook)

## Development

To continue development:

1. **Next Priority**: Complete the IMAP Core Implementation task
2. **Add Account Setup UI**: Create a dialog for adding/editing accounts
3. **Implement Email Operations**: Complete read, delete, move operations
4. **Add Email Composition**: Implement sending emails via SMTP

## Troubleshooting

**Build Errors:**
- Ensure .NET 8 SDK is installed
- Run `dotnet restore` to restore packages

**Runtime Errors:**
- Check logs in the `logs/` directory
- Verify IMAP server settings and credentials

**Connection Issues:**
- Ensure firewall allows the application
- Check if email provider requires app-specific passwords
- Verify IMAP is enabled in email account settings

## License

This project is for educational and personal use.
