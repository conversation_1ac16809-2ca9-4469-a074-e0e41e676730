<Window x:Class="EmailClient.Views.ContactsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Contacts" Height="600" Width="800"
        WindowStartupLocation="CenterOwner">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Toolbar -->
        <ToolBar Grid.Row="0">
            <Button Content="Sync Now" Command="{Binding SyncContactsCommand}"/>
            <Separator/>
            <TextBox x:Name="SearchBox" Width="200" 
                     Text="{Binding SearchQuery, UpdateSourceTrigger=PropertyChanged}"
                     ToolTip="Search contacts..."/>
            <Button Content="Search" Command="{Binding SearchCommand}"/>
            <Button Content="Clear" Command="{Binding ClearSearchCommand}"/>
        </ToolBar>
        
        <!-- Contacts List -->
        <ListView Grid.Row="1" ItemsSource="{Binding Contacts}" SelectedItem="{Binding SelectedContact}">
            <ListView.View>
                <GridView>
                    <GridViewColumn Header="Name" Width="200">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <Image Source="{Binding PhotoUrl}" Width="32" Height="32" 
                                           Margin="0,0,8,0" RenderOptions.BitmapScalingMode="HighQuality"/>
                                    <TextBlock Text="{Binding DisplayName}" VerticalAlignment="Center"/>
                                </StackPanel>
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                    <GridViewColumn Header="Email" Width="250" DisplayMemberBinding="{Binding PrimaryEmail}"/>
                    <GridViewColumn Header="Phone" Width="150" DisplayMemberBinding="{Binding PrimaryPhone}"/>
                    <GridViewColumn Header="Organization" Width="150" DisplayMemberBinding="{Binding PrimaryOrganization}"/>
                    <GridViewColumn Header="Last Sync" Width="120" DisplayMemberBinding="{Binding LastSyncAt, StringFormat='{}{0:MM/dd/yyyy}'}"/>
                </GridView>
            </ListView.View>
        </ListView>
        
        <!-- Status Bar -->
        <StatusBar Grid.Row="2">
            <StatusBarItem>
                <TextBlock Text="{Binding StatusMessage}"/>
            </StatusBarItem>
            <StatusBarItem HorizontalAlignment="Right">
                <TextBlock Text="{Binding ContactsCountDisplay}"/>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>
