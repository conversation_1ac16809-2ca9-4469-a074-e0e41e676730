using Microsoft.EntityFrameworkCore;
using EmailClient.Models;

namespace EmailClient.Data;

public class EmailDbContext : DbContext
{
    public EmailDbContext(DbContextOptions<EmailDbContext> options) : base(options)
    {
    }

    public DbSet<EmailAccount> Accounts { get; set; }
    public DbSet<EmailMessage> Messages { get; set; }
    public DbSet<EmailFolder> Folders { get; set; }
    public DbSet<EmailAttachment> Attachments { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // EmailAccount configuration
        modelBuilder.Entity<EmailAccount>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasIndex(e => e.EmailAddress).IsUnique();
            entity.Property(e => e.Name).HasMaxLength(100);
            entity.Property(e => e.EmailAddress).HasMaxLength(255);
            entity.Property(e => e.ImapServer).HasMaxLength(255);
            entity.Property(e => e.Username).HasMaxLength(255);
            entity.Property(e => e.Password).HasMaxLength(500); // Encrypted password
        });

        // EmailMessage configuration
        modelBuilder.Entity<EmailMessage>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasIndex(e => new { e.AccountId, e.MessageId }).IsUnique();
            entity.Property(e => e.MessageId).HasMaxLength(255);
            entity.Property(e => e.Subject).HasMaxLength(500);
            entity.Property(e => e.FromAddress).HasMaxLength(255);
            entity.Property(e => e.FromName).HasMaxLength(255);
            
            entity.HasOne(e => e.Account)
                .WithMany(a => a.Messages)
                .HasForeignKey(e => e.AccountId)
                .OnDelete(DeleteBehavior.Cascade);
                
            entity.HasOne(e => e.Folder)
                .WithMany(f => f.Messages)
                .HasForeignKey(e => e.FolderId)
                .OnDelete(DeleteBehavior.SetNull);
        });

        // EmailFolder configuration
        modelBuilder.Entity<EmailFolder>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasIndex(e => new { e.AccountId, e.FullName }).IsUnique();
            entity.Property(e => e.Name).HasMaxLength(255);
            entity.Property(e => e.FullName).HasMaxLength(500);
            
            entity.HasOne(e => e.Account)
                .WithMany(a => a.Folders)
                .HasForeignKey(e => e.AccountId)
                .OnDelete(DeleteBehavior.Cascade);
                
            entity.HasOne(e => e.ParentFolder)
                .WithMany(f => f.SubFolders)
                .HasForeignKey(e => e.ParentFolderId)
                .OnDelete(DeleteBehavior.Restrict);
        });

        // EmailAttachment configuration
        modelBuilder.Entity<EmailAttachment>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.FileName).HasMaxLength(255);
            entity.Property(e => e.ContentType).HasMaxLength(100);
            entity.Property(e => e.ContentId).HasMaxLength(255);
            entity.Property(e => e.FilePath).HasMaxLength(500);
            
            entity.HasOne(e => e.Message)
                .WithMany(m => m.Attachments)
                .HasForeignKey(e => e.MessageId)
                .OnDelete(DeleteBehavior.Cascade);
        });
    }
}
