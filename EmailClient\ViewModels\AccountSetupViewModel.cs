using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using EmailClient.Models;
using EmailClient.Services;
using Microsoft.Extensions.Logging;
using System.Windows;
using System.Windows.Media;

namespace EmailClient.ViewModels;

public partial class AccountSetupViewModel : ViewModelBase
{
    private readonly IAccountService _accountService;
    private readonly ILogger<AccountSetupViewModel> _logger;
    private Window? _window;

    [ObservableProperty]
    private string _accountName = string.Empty;

    [ObservableProperty]
    private string _emailAddress = string.Empty;

    [ObservableProperty]
    private string _username = string.Empty;

    [ObservableProperty]
    private string _password = string.Empty;

    [ObservableProperty]
    private string _imapServer = string.Empty;

    [ObservableProperty]
    private int _imapPort = 993;

    [ObservableProperty]
    private bool _useSsl = true;

    [ObservableProperty]
    private string _testResult = string.Empty;

    [ObservableProperty]
    private Brush _testResultColor = Brushes.Black;

    [ObservableProperty]
    private bool _isTesting = false;

    public bool DialogResult { get; private set; }

    public AccountSetupViewModel(IAccountService accountService, ILogger<AccountSetupViewModel> logger)
    {
        _accountService = accountService;
        _logger = logger;
    }

    public void SetWindow(Window window)
    {
        _window = window;
    }

    public bool CanTestConnection => !string.IsNullOrWhiteSpace(EmailAddress) &&
                                   !string.IsNullOrWhiteSpace(Username) &&
                                   !string.IsNullOrWhiteSpace(Password) &&
                                   !string.IsNullOrWhiteSpace(ImapServer) &&
                                   !IsTesting;

    public bool CanSave => !string.IsNullOrWhiteSpace(AccountName) &&
                          !string.IsNullOrWhiteSpace(EmailAddress) &&
                          !string.IsNullOrWhiteSpace(Username) &&
                          !string.IsNullOrWhiteSpace(Password) &&
                          !string.IsNullOrWhiteSpace(ImapServer) &&
                          !IsTesting;

    partial void OnEmailAddressChanged(string value)
    {
        if (string.IsNullOrWhiteSpace(Username))
        {
            Username = value;
        }
        if (string.IsNullOrWhiteSpace(AccountName) && !string.IsNullOrWhiteSpace(value))
        {
            AccountName = value;
        }
        OnPropertyChanged(nameof(CanTestConnection));
        OnPropertyChanged(nameof(CanSave));
    }

    partial void OnUsernameChanged(string value)
    {
        OnPropertyChanged(nameof(CanTestConnection));
        OnPropertyChanged(nameof(CanSave));
    }

    partial void OnPasswordChanged(string value)
    {
        OnPropertyChanged(nameof(CanTestConnection));
        OnPropertyChanged(nameof(CanSave));
    }

    partial void OnAccountNameChanged(string value)
    {
        OnPropertyChanged(nameof(CanSave));
    }

    partial void OnImapServerChanged(string value)
    {
        OnPropertyChanged(nameof(CanTestConnection));
        OnPropertyChanged(nameof(CanSave));
    }

    partial void OnIsTestingChanged(bool value)
    {
        OnPropertyChanged(nameof(CanTestConnection));
        OnPropertyChanged(nameof(CanSave));
    }

    [RelayCommand]
    private void SetupGmail()
    {
        ImapServer = "imap.gmail.com";
        ImapPort = 993;
        UseSsl = true;
        TestResult = "Gmail settings applied. Use an app-specific password.";
        TestResultColor = Brushes.Blue;
    }

    [RelayCommand]
    private void SetupOutlook()
    {
        ImapServer = "outlook.office365.com";
        ImapPort = 993;
        UseSsl = true;
        TestResult = "Outlook settings applied.";
        TestResultColor = Brushes.Blue;
    }

    [RelayCommand]
    private void SetupYahoo()
    {
        ImapServer = "imap.mail.yahoo.com";
        ImapPort = 993;
        UseSsl = true;
        TestResult = "Yahoo settings applied.";
        TestResultColor = Brushes.Blue;
    }

    [RelayCommand]
    private async Task TestConnection()
    {
        IsTesting = true;
        TestResult = "Testing connection...";
        TestResultColor = Brushes.Blue;

        try
        {
            var testAccount = CreateEmailAccount();
            var success = await _accountService.TestAccountConnectionAsync(testAccount);

            if (success)
            {
                TestResult = "✓ Connection successful!";
                TestResultColor = Brushes.Green;
            }
            else
            {
                TestResult = "✗ Connection failed. Check your settings.";
                TestResultColor = Brushes.Red;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Connection test failed");
            TestResult = $"✗ Error: {ex.Message}";
            TestResultColor = Brushes.Red;
        }
        finally
        {
            IsTesting = false;
        }
    }

    [RelayCommand]
    private async Task Save()
    {
        try
        {
            var account = CreateEmailAccount();
            await _accountService.CreateAccountAsync(account);

            DialogResult = true;
            _window?.Close();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to save account");
            MessageBox.Show($"Failed to save account: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    [RelayCommand]
    private void Cancel()
    {
        DialogResult = false;
        _window?.Close();
    }

    private EmailAccount CreateEmailAccount()
    {
        return new EmailAccount
        {
            Name = AccountName.Trim(),
            EmailAddress = EmailAddress.Trim(),
            Username = Username.Trim(),
            Password = Password, // In production, this should be encrypted
            ImapServer = ImapServer.Trim(),
            ImapPort = ImapPort,
            UseSsl = UseSsl,
            IsEnabled = true
        };
    }
}
