<Window x:Class="EmailClient.Views.AccountSetupWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Add Email Account" Height="500" Width="450"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <TextBlock Grid.Row="0" Text="Add Email Account" FontSize="18" FontWeight="Bold" Margin="0,0,0,20"/>
        
        <!-- Form -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- Account Name -->
                <Label Content="Account Name:" Target="{Binding ElementName=AccountNameTextBox}"/>
                <TextBox x:Name="AccountNameTextBox" 
                         Text="{Binding AccountName, UpdateSourceTrigger=PropertyChanged}"
                         Margin="0,0,0,10"/>
                
                <!-- Email Address -->
                <Label Content="Email Address:" Target="{Binding ElementName=EmailAddressTextBox}"/>
                <TextBox x:Name="EmailAddressTextBox" 
                         Text="{Binding EmailAddress, UpdateSourceTrigger=PropertyChanged}"
                         Margin="0,0,0,10"/>
                
                <!-- Username -->
                <Label Content="Username:" Target="{Binding ElementName=UsernameTextBox}"/>
                <TextBox x:Name="UsernameTextBox" 
                         Text="{Binding Username, UpdateSourceTrigger=PropertyChanged}"
                         Margin="0,0,0,10"/>
                <TextBlock Text="Usually the same as email address" 
                           Foreground="Gray" FontSize="11" Margin="0,-8,0,10"/>
                
                <!-- Password -->
                <Label Content="Password:" Target="{Binding ElementName=PasswordBox}"/>
                <PasswordBox x:Name="PasswordBox" 
                             PasswordChanged="PasswordBox_PasswordChanged"
                             Margin="0,0,0,10"/>
                <TextBlock Text="Use app-specific password for Gmail/Outlook" 
                           Foreground="Gray" FontSize="11" Margin="0,-8,0,10"/>
                
                <!-- IMAP Server Settings -->
                <GroupBox Header="IMAP Server Settings" Margin="0,10,0,10">
                    <StackPanel Margin="10">
                        <!-- IMAP Server -->
                        <Label Content="IMAP Server:" Target="{Binding ElementName=ImapServerTextBox}"/>
                        <TextBox x:Name="ImapServerTextBox" 
                                 Text="{Binding ImapServer, UpdateSourceTrigger=PropertyChanged}"
                                 Margin="0,0,0,10"/>
                        
                        <!-- Port and SSL -->
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0">
                                <Label Content="Port:" Target="{Binding ElementName=PortTextBox}"/>
                                <TextBox x:Name="PortTextBox" 
                                         Text="{Binding ImapPort, UpdateSourceTrigger=PropertyChanged}"
                                         Width="80" HorizontalAlignment="Left"/>
                            </StackPanel>
                            
                            <CheckBox Grid.Column="1" 
                                      Content="Use SSL" 
                                      IsChecked="{Binding UseSsl}"
                                      VerticalAlignment="Bottom" Margin="10,0,0,5"/>
                        </Grid>
                    </StackPanel>
                </GroupBox>
                
                <!-- Quick Setup -->
                <GroupBox Header="Quick Setup" Margin="0,10,0,10">
                    <StackPanel Margin="10">
                        <TextBlock Text="Select your email provider for automatic configuration:" 
                                   Margin="0,0,0,10"/>
                        <StackPanel Orientation="Horizontal">
                            <Button Content="Gmail" Command="{Binding SetupGmailCommand}" Margin="0,0,10,0"/>
                            <Button Content="Outlook" Command="{Binding SetupOutlookCommand}" Margin="0,0,10,0"/>
                            <Button Content="Yahoo" Command="{Binding SetupYahooCommand}"/>
                        </StackPanel>
                    </StackPanel>
                </GroupBox>
            </StackPanel>
        </ScrollViewer>
        
        <!-- Test Connection -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" Margin="0,10,0,10">
            <Button Content="Test Connection" 
                    Command="{Binding TestConnectionCommand}"
                    IsEnabled="{Binding CanTestConnection}"/>
            <TextBlock Text="{Binding TestResult}" 
                       Margin="10,0,0,0" 
                       VerticalAlignment="Center"
                       Foreground="{Binding TestResultColor}"/>
        </StackPanel>
        
        <!-- Buttons -->
        <StackPanel Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Right">
            <Button Content="OK" 
                    Command="{Binding SaveCommand}"
                    IsEnabled="{Binding CanSave}"
                    IsDefault="True" 
                    Width="75" Margin="0,0,10,0"/>
            <Button Content="Cancel" 
                    Command="{Binding CancelCommand}"
                    IsCancel="True" 
                    Width="75"/>
        </StackPanel>
    </Grid>
</Window>
