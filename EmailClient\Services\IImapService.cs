using EmailClient.Models;
using MailKit.Net.Imap;

namespace EmailClient.Services;

public interface IImapService
{
    Task<bool> TestConnectionAsync(EmailAccount account);
    Task<ImapClient> ConnectAsync(EmailAccount account);
    Task<IEnumerable<EmailFolder>> GetFoldersAsync(EmailAccount account);
    Task<IEnumerable<EmailMessage>> GetMessagesAsync(EmailAccount account, EmailFolder folder, int limit = 50);
    Task<EmailMessage> GetMessageDetailsAsync(EmailAccount account, EmailFolder folder, string messageId);
    Task MarkAsReadAsync(EmailAccount account, EmailFolder folder, string messageId);
    Task MarkAsUnreadAsync(EmailAccount account, EmailFolder folder, string messageId);
    Task DeleteMessageAsync(EmailAccount account, EmailFolder folder, string messageId);
    Task MoveMessageAsync(EmailAccount account, EmailFolder sourceFolder, EmailFolder targetFolder, string messageId);
    Task<IEnumerable<EmailMessage>> SearchMessagesAsync(EmailAccount account, EmailFolder folder, string searchQuery);
}
