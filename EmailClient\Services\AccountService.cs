using EmailClient.Data;
using EmailClient.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EmailClient.Services;

public class AccountService : IAccountService
{
    private readonly EmailDbContext _context;
    private readonly IImapService _imapService;
    private readonly ILogger<AccountService> _logger;

    public AccountService(EmailDbContext context, IImapService imapService, ILogger<AccountService> logger)
    {
        _context = context;
        _imapService = imapService;
        _logger = logger;
    }

    public async Task<IEnumerable<EmailAccount>> GetAllAccountsAsync()
    {
        return await _context.Accounts
            .OrderBy(a => a.Name)
            .ToListAsync();
    }

    public async Task<EmailAccount?> GetAccountAsync(int id)
    {
        return await _context.Accounts
            .Include(a => a.Folders)
            .FirstOrDefaultAsync(a => a.Id == id);
    }

    public async Task<EmailAccount> CreateAccountAsync(EmailAccount account)
    {
        // Test connection before saving
        var connectionTest = await _imapService.TestConnectionAsync(account);
        if (!connectionTest)
        {
            throw new InvalidOperationException("Failed to connect to the IMAP server. Please check your settings.");
        }

        _context.Accounts.Add(account);
        await _context.SaveChangesAsync();

        _logger.LogInformation("Created new email account: {Email}", account.EmailAddress);

        // Initialize folders for the new account
        try
        {
            var folders = await _imapService.GetFoldersAsync(account);
            foreach (var folder in folders)
            {
                folder.AccountId = account.Id;
                _context.Folders.Add(folder);
            }
            await _context.SaveChangesAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize folders for account {Email}", account.EmailAddress);
        }

        return account;
    }

    public async Task<EmailAccount> UpdateAccountAsync(EmailAccount account)
    {
        var existing = await _context.Accounts.FindAsync(account.Id);
        if (existing == null)
        {
            throw new ArgumentException("Account not found");
        }

        // Test connection with new settings
        var connectionTest = await _imapService.TestConnectionAsync(account);
        if (!connectionTest)
        {
            throw new InvalidOperationException("Failed to connect to the IMAP server with the updated settings.");
        }

        existing.Name = account.Name;
        existing.EmailAddress = account.EmailAddress;
        existing.ImapServer = account.ImapServer;
        existing.ImapPort = account.ImapPort;
        existing.UseSsl = account.UseSsl;
        existing.Username = account.Username;
        existing.Password = account.Password;
        existing.IsEnabled = account.IsEnabled;

        await _context.SaveChangesAsync();

        _logger.LogInformation("Updated email account: {Email}", account.EmailAddress);
        return existing;
    }

    public async Task DeleteAccountAsync(int id)
    {
        var account = await _context.Accounts.FindAsync(id);
        if (account != null)
        {
            _context.Accounts.Remove(account);
            await _context.SaveChangesAsync();
            _logger.LogInformation("Deleted email account: {Email}", account.EmailAddress);
        }
    }

    public async Task<bool> TestAccountConnectionAsync(EmailAccount account)
    {
        try
        {
            return await _imapService.TestConnectionAsync(account);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Connection test failed for account {Email}", account.EmailAddress);
            return false;
        }
    }

    public async Task<IEnumerable<EmailFolder>> GetAccountFoldersAsync(int accountId)
    {
        return await _context.Folders
            .Where(f => f.AccountId == accountId)
            .OrderBy(f => f.Type)
            .ThenBy(f => f.Name)
            .ToListAsync();
    }
}
