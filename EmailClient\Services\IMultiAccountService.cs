using EmailClient.Models;

namespace EmailClient.Services;

public interface IMultiAccountService
{
    Task<UnifiedInboxResult> GetUnifiedInboxAsync(int pageSize = 50, int pageNumber = 1);
    Task<AccountSummary> GetAccountSummaryAsync(int accountId);
    Task<IEnumerable<AccountSummary>> GetAllAccountSummariesAsync();
    Task<CrossAccountSearchResult> SearchAcrossAccountsAsync(string query, int maxResults = 100);
    Task<bool> MoveMessageBetweenAccountsAsync(int messageId, int targetAccountId, string targetFolderName);
    Task SyncAccountsInParallelAsync(IEnumerable<int> accountIds);
    Task<AccountHealthStatus> CheckAccountHealthAsync(int accountId);
    Task<IEnumerable<EmailMessage>> GetRecentMessagesAcrossAccountsAsync(int hours = 24);
}

public class UnifiedInboxResult
{
    public IEnumerable<EmailMessage> Messages { get; set; } = new List<EmailMessage>();
    public int TotalCount { get; set; }
    public int UnreadCount { get; set; }
    public int PageNumber { get; set; }
    public int PageSize { get; set; }
    public bool HasNextPage { get; set; }
    public Dictionary<int, int> MessageCountByAccount { get; set; } = new();
}

public class AccountSummary
{
    public int AccountId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string EmailAddress { get; set; } = string.Empty;
    public bool IsEnabled { get; set; }
    public bool IsConnected { get; set; }
    public DateTime LastSyncAt { get; set; }
    public int TotalMessages { get; set; }
    public int UnreadMessages { get; set; }
    public int InboxMessages { get; set; }
    public long TotalSize { get; set; }
    public string Status { get; set; } = string.Empty;
    public string? LastError { get; set; }
}

public class CrossAccountSearchResult
{
    public IEnumerable<EmailMessage> Messages { get; set; } = new List<EmailMessage>();
    public Dictionary<int, int> ResultsByAccount { get; set; } = new();
    public int TotalResults { get; set; }
    public TimeSpan SearchDuration { get; set; }
}

public class AccountHealthStatus
{
    public int AccountId { get; set; }
    public bool IsHealthy { get; set; }
    public bool CanConnect { get; set; }
    public bool CanAuthenticate { get; set; }
    public bool CanAccessInbox { get; set; }
    public IEnumerable<string> Issues { get; set; } = new List<string>();
    public DateTime LastChecked { get; set; }
}
