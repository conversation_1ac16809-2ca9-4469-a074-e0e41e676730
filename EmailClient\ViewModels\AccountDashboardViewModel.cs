using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using EmailClient.Services;
using Microsoft.Extensions.Logging;
using System.Collections.ObjectModel;
using System.Windows;

namespace EmailClient.ViewModels;

public partial class AccountDashboardViewModel : ViewModelBase
{
    private readonly IMultiAccountService _multiAccountService;
    private readonly ILogger<AccountDashboardViewModel> _logger;

    [ObservableProperty]
    private ObservableCollection<AccountSummaryViewModel> _accountSummaries = new();

    [ObservableProperty]
    private AccountSummaryViewModel? _selectedAccount;

    [ObservableProperty]
    private bool _isLoading = false;

    [ObservableProperty]
    private string _statusMessage = "Ready";

    [ObservableProperty]
    private int _totalAccounts;

    [ObservableProperty]
    private int _connectedAccounts;

    [ObservableProperty]
    private int _totalUnreadMessages;

    [ObservableProperty]
    private long _totalStorageUsed;

    public AccountDashboardViewModel(IMultiAccountService multiAccountService, ILogger<AccountDashboardViewModel> logger)
    {
        _multiAccountService = multiAccountService;
        _logger = logger;

        _ = LoadAccountSummariesAsync();
    }

    private async Task LoadAccountSummariesAsync()
    {
        IsLoading = true;
        StatusMessage = "Loading account summaries...";

        try
        {
            var summaries = await _multiAccountService.GetAllAccountSummariesAsync();
            
            AccountSummaries.Clear();
            foreach (var summary in summaries)
            {
                AccountSummaries.Add(new AccountSummaryViewModel(summary));
            }

            UpdateOverallStatistics();
            StatusMessage = "Account summaries loaded";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load account summaries");
            StatusMessage = "Failed to load account summaries";
        }
        finally
        {
            IsLoading = false;
        }
    }

    private void UpdateOverallStatistics()
    {
        TotalAccounts = AccountSummaries.Count;
        ConnectedAccounts = AccountSummaries.Count(a => a.IsConnected);
        TotalUnreadMessages = AccountSummaries.Sum(a => a.UnreadMessages);
        TotalStorageUsed = AccountSummaries.Sum(a => a.TotalSize);
    }

    [RelayCommand]
    private async Task RefreshSummaries()
    {
        await LoadAccountSummariesAsync();
    }

    [RelayCommand]
    private async Task CheckAccountHealth()
    {
        if (SelectedAccount == null) return;

        try
        {
            StatusMessage = $"Checking health for {SelectedAccount.Name}...";
            var health = await _multiAccountService.CheckAccountHealthAsync(SelectedAccount.AccountId);
            
            SelectedAccount.UpdateHealthStatus(health);
            
            if (health.IsHealthy)
            {
                StatusMessage = $"{SelectedAccount.Name} is healthy";
            }
            else
            {
                StatusMessage = $"{SelectedAccount.Name} has issues: {string.Join(", ", health.Issues)}";
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to check account health");
            StatusMessage = "Failed to check account health";
        }
    }

    [RelayCommand]
    private async Task SyncSelectedAccount()
    {
        if (SelectedAccount == null) return;

        try
        {
            StatusMessage = $"Syncing {SelectedAccount.Name}...";
            await _multiAccountService.SyncAccountsInParallelAsync(new[] { SelectedAccount.AccountId });
            await LoadAccountSummariesAsync();
            StatusMessage = $"Sync completed for {SelectedAccount.Name}";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to sync account");
            StatusMessage = "Failed to sync account";
        }
    }

    [RelayCommand]
    private async Task SyncAllAccounts()
    {
        try
        {
            StatusMessage = "Syncing all accounts...";
            var accountIds = AccountSummaries.Select(a => a.AccountId).ToArray();
            await _multiAccountService.SyncAccountsInParallelAsync(accountIds);
            await LoadAccountSummariesAsync();
            StatusMessage = "All accounts synced";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to sync all accounts");
            StatusMessage = "Failed to sync all accounts";
        }
    }

    public string TotalStorageDisplay => FormatBytes(TotalStorageUsed);

    private static string FormatBytes(long bytes)
    {
        string[] suffixes = { "B", "KB", "MB", "GB", "TB" };
        int counter = 0;
        decimal number = bytes;
        while (Math.Round(number / 1024) >= 1)
        {
            number /= 1024;
            counter++;
        }
        return $"{number:n1} {suffixes[counter]}";
    }
}

public partial class AccountSummaryViewModel : ViewModelBase
{
    [ObservableProperty]
    private int _accountId;

    [ObservableProperty]
    private string _name = string.Empty;

    [ObservableProperty]
    private string _emailAddress = string.Empty;

    [ObservableProperty]
    private bool _isEnabled;

    [ObservableProperty]
    private bool _isConnected;

    [ObservableProperty]
    private DateTime _lastSyncAt;

    [ObservableProperty]
    private int _totalMessages;

    [ObservableProperty]
    private int _unreadMessages;

    [ObservableProperty]
    private int _inboxMessages;

    [ObservableProperty]
    private long _totalSize;

    [ObservableProperty]
    private string _status = string.Empty;

    [ObservableProperty]
    private string? _lastError;

    [ObservableProperty]
    private bool _isHealthy = true;

    [ObservableProperty]
    private string _healthStatus = "Unknown";

    public AccountSummaryViewModel(AccountSummary summary)
    {
        AccountId = summary.AccountId;
        Name = summary.Name;
        EmailAddress = summary.EmailAddress;
        IsEnabled = summary.IsEnabled;
        IsConnected = summary.IsConnected;
        LastSyncAt = summary.LastSyncAt;
        TotalMessages = summary.TotalMessages;
        UnreadMessages = summary.UnreadMessages;
        InboxMessages = summary.InboxMessages;
        TotalSize = summary.TotalSize;
        Status = summary.Status;
        LastError = summary.LastError;
    }

    public void UpdateHealthStatus(AccountHealthStatus health)
    {
        IsHealthy = health.IsHealthy;
        HealthStatus = health.IsHealthy ? "Healthy" : $"Issues: {string.Join(", ", health.Issues)}";
    }

    public string LastSyncDisplay => LastSyncAt == default ? "Never" : LastSyncAt.ToString("MM/dd/yyyy HH:mm");
    public string TotalSizeDisplay => FormatBytes(TotalSize);
    public string UnreadDisplay => UnreadMessages > 0 ? $"{UnreadMessages} unread" : "No unread";

    private static string FormatBytes(long bytes)
    {
        string[] suffixes = { "B", "KB", "MB", "GB", "TB" };
        int counter = 0;
        decimal number = bytes;
        while (Math.Round(number / 1024) >= 1)
        {
            number /= 1024;
            counter++;
        }
        return $"{number:n1} {suffixes[counter]}";
    }
}
