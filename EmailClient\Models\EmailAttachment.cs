using System.ComponentModel.DataAnnotations;

namespace EmailClient.Models;

public class EmailAttachment
{
    public int Id { get; set; }
    
    public int MessageId { get; set; }
    public virtual EmailMessage Message { get; set; } = null!;
    
    [Required]
    public string FileName { get; set; } = string.Empty;
    
    public string ContentType { get; set; } = string.Empty;
    
    public long Size { get; set; }
    
    public string ContentId { get; set; } = string.Empty; // For inline attachments
    
    public bool IsInline { get; set; } = false;
    
    public byte[]? Data { get; set; } // Store small attachments directly, larger ones as file paths
    
    public string? FilePath { get; set; } // Path to stored file for large attachments
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
}
