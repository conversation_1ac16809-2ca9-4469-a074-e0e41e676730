namespace EmailClient.Services;

public interface ISettingsService
{
    Task<T?> GetSettingAsync<T>(string key);
    Task SetSettingAsync<T>(string key, T value);
    Task<bool> GetBoolSettingAsync(string key, bool defaultValue = false);
    Task<int> GetIntSettingAsync(string key, int defaultValue = 0);
    Task<string> GetStringSettingAsync(string key, string defaultValue = "");
    Task SetBoolSettingAsync(string key, bool value);
    Task SetIntSettingAsync(string key, int value);
    Task SetStringSettingAsync(string key, string value);
}
