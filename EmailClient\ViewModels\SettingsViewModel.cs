using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using EmailClient.Models;
using EmailClient.Services;
using Microsoft.Extensions.Logging;
using System.Collections.ObjectModel;
using System.Windows;

namespace EmailClient.ViewModels;

public partial class SettingsViewModel : ViewModelBase
{
    private readonly ISettingsService _settingsService;
    private readonly IAccountService _accountService;
    private readonly IGoogleAuthService _googleAuthService;
    private readonly IGoogleContactsService _googleContactsService;
    private readonly IGoogleCalendarService _googleCalendarService;
    private readonly ILogger<SettingsViewModel> _logger;
    private Window? _window;

    [ObservableProperty]
    private int _syncIntervalMinutes = 15;

    [ObservableProperty]
    private bool _autoSyncEnabled = true;

    [ObservableProperty]
    private bool _syncOnStartup = true;

    [ObservableProperty]
    private int _messagesPerPage = 50;

    [ObservableProperty]
    private bool _showMessagePreview = true;

    [ObservableProperty]
    private bool _markReadOnSelect = true;

    [ObservableProperty]
    private ObservableCollection<EmailAccount> _accounts = new();

    [ObservableProperty]
    private EmailAccount? _selectedAccount;

    // Google Integration Properties
    [ObservableProperty]
    private string _googleClientId = string.Empty;

    [ObservableProperty]
    private string _googleClientSecret = string.Empty;

    [ObservableProperty]
    private bool _contactsSyncEnabled = true;

    [ObservableProperty]
    private bool _calendarSyncEnabled = true;

    [ObservableProperty]
    private int _contactsSyncIntervalMinutes = 60;

    [ObservableProperty]
    private int _calendarSyncIntervalMinutes = 30;

    [ObservableProperty]
    private bool _isGoogleAuthenticated = false;

    [ObservableProperty]
    private string _googleAuthStatus = "Not authenticated";

    [ObservableProperty]
    private string _googleUserInfo = string.Empty;

    [ObservableProperty]
    private string _lastContactsSync = "Never";

    [ObservableProperty]
    private string _lastCalendarSync = "Never";

    [ObservableProperty]
    private int _contactsCount = 0;

    [ObservableProperty]
    private int _calendarEventsCount = 0;

    public bool DialogResult { get; private set; }

    public string SyncIntervalDisplay => $"{SyncIntervalMinutes} minutes";
    public string ContactsSyncIntervalDisplay => FormatSyncInterval(ContactsSyncIntervalMinutes);
    public string CalendarSyncIntervalDisplay => FormatSyncInterval(CalendarSyncIntervalMinutes);
    public bool CanAuthenticateGoogle => !string.IsNullOrWhiteSpace(GoogleClientId) && !string.IsNullOrWhiteSpace(GoogleClientSecret);

    public SettingsViewModel(
        ISettingsService settingsService,
        IAccountService accountService,
        IGoogleAuthService googleAuthService,
        IGoogleContactsService googleContactsService,
        IGoogleCalendarService googleCalendarService,
        ILogger<SettingsViewModel> logger)
    {
        _settingsService = settingsService;
        _accountService = accountService;
        _googleAuthService = googleAuthService;
        _googleContactsService = googleContactsService;
        _googleCalendarService = googleCalendarService;
        _logger = logger;

        _ = LoadSettingsAsync();
    }

    public void SetWindow(Window window)
    {
        _window = window;
    }

    private async Task LoadSettingsAsync()
    {
        try
        {
            // Load settings
            SyncIntervalMinutes = await _settingsService.GetIntSettingAsync("SyncIntervalMinutes", 15);
            AutoSyncEnabled = await _settingsService.GetBoolSettingAsync("AutoSyncEnabled", true);
            SyncOnStartup = await _settingsService.GetBoolSettingAsync("SyncOnStartup", true);
            MessagesPerPage = await _settingsService.GetIntSettingAsync("MessagesPerPage", 50);
            ShowMessagePreview = await _settingsService.GetBoolSettingAsync("ShowMessagePreview", true);
            MarkReadOnSelect = await _settingsService.GetBoolSettingAsync("MarkReadOnSelect", true);

            // Load accounts
            var accounts = await _accountService.GetAllAccountsAsync();
            Accounts.Clear();
            foreach (var account in accounts)
            {
                Accounts.Add(account);
            }

            // Load Google settings
            await LoadGoogleSettingsAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load settings");
        }
    }

    partial void OnSyncIntervalMinutesChanged(int value)
    {
        OnPropertyChanged(nameof(SyncIntervalDisplay));
    }

    partial void OnContactsSyncIntervalMinutesChanged(int value)
    {
        OnPropertyChanged(nameof(ContactsSyncIntervalDisplay));
    }

    partial void OnCalendarSyncIntervalMinutesChanged(int value)
    {
        OnPropertyChanged(nameof(CalendarSyncIntervalDisplay));
    }

    partial void OnGoogleClientIdChanged(string value)
    {
        OnPropertyChanged(nameof(CanAuthenticateGoogle));
    }

    partial void OnGoogleClientSecretChanged(string value)
    {
        OnPropertyChanged(nameof(CanAuthenticateGoogle));
    }

    [RelayCommand]
    private void AddAccount()
    {
        // This would open the account setup dialog
        // For now, just show a message
        MessageBox.Show("Add account functionality - use the main menu to add accounts", "Info", MessageBoxButton.OK, MessageBoxImage.Information);
    }

    [RelayCommand]
    private void EditAccount()
    {
        if (SelectedAccount == null) return;
        
        // This would open the account edit dialog
        MessageBox.Show($"Edit account functionality for {SelectedAccount.Name}", "Info", MessageBoxButton.OK, MessageBoxImage.Information);
    }

    [RelayCommand]
    private async Task RemoveAccount()
    {
        if (SelectedAccount == null) return;

        var result = MessageBox.Show($"Are you sure you want to remove the account '{SelectedAccount.Name}'?", 
            "Confirm Remove", MessageBoxButton.YesNo, MessageBoxImage.Question);

        if (result == MessageBoxResult.Yes)
        {
            try
            {
                await _accountService.DeleteAccountAsync(SelectedAccount.Id);
                Accounts.Remove(SelectedAccount);
                SelectedAccount = null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to remove account");
                MessageBox.Show("Failed to remove account", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }

    [RelayCommand]
    private async Task Save()
    {
        try
        {
            // Save settings
            await _settingsService.SetIntSettingAsync("SyncIntervalMinutes", SyncIntervalMinutes);
            await _settingsService.SetBoolSettingAsync("AutoSyncEnabled", AutoSyncEnabled);
            await _settingsService.SetBoolSettingAsync("SyncOnStartup", SyncOnStartup);
            await _settingsService.SetIntSettingAsync("MessagesPerPage", MessagesPerPage);
            await _settingsService.SetBoolSettingAsync("ShowMessagePreview", ShowMessagePreview);
            await _settingsService.SetBoolSettingAsync("MarkReadOnSelect", MarkReadOnSelect);

            // Save account changes
            foreach (var account in Accounts)
            {
                await _accountService.UpdateAccountAsync(account);
            }

            // Save Google settings
            await SaveGoogleSettingsAsync();

            DialogResult = true;
            _window?.Close();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to save settings");
            MessageBox.Show("Failed to save settings", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    [RelayCommand]
    private void Cancel()
    {
        DialogResult = false;
        _window?.Close();
    }

    [RelayCommand]
    private async Task AuthenticateGoogle()
    {
        try
        {
            GoogleAuthStatus = "Authenticating...";

            var success = await _googleAuthService.AuthenticateAsync(
                GoogleClientId,
                GoogleClientSecret,
                GoogleScopes.DefaultScopes);

            if (success)
            {
                await LoadGoogleAuthStatusAsync();
            }
            else
            {
                GoogleAuthStatus = "Authentication failed";
                IsGoogleAuthenticated = false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to authenticate with Google");
            GoogleAuthStatus = "Authentication error";
            IsGoogleAuthenticated = false;
        }
    }

    [RelayCommand]
    private async Task RevokeGoogleAuth()
    {
        try
        {
            await _googleAuthService.RevokeAuthenticationAsync();
            GoogleAuthStatus = "Not authenticated";
            GoogleUserInfo = string.Empty;
            IsGoogleAuthenticated = false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to revoke Google authentication");
        }
    }

    [RelayCommand]
    private async Task SyncContacts()
    {
        try
        {
            var count = await _googleContactsService.SyncContactsAsync(fullSync: true);
            LastContactsSync = DateTime.Now.ToString("g");
            ContactsCount = (await _googleContactsService.GetContactsAsync()).Count();

            MessageBox.Show($"Synchronized {count} contacts", "Sync Complete",
                MessageBoxButton.OK, MessageBoxImage.Information);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to sync contacts");
            MessageBox.Show("Failed to sync contacts", "Error",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    [RelayCommand]
    private async Task SyncCalendar()
    {
        try
        {
            var count = await _googleCalendarService.SyncEventsAsync(fullSync: true);
            LastCalendarSync = DateTime.Now.ToString("g");
            CalendarEventsCount = (await _googleCalendarService.GetEventsAsync()).Count();

            MessageBox.Show($"Synchronized {count} calendar events", "Sync Complete",
                MessageBoxButton.OK, MessageBoxImage.Information);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to sync calendar");
            MessageBox.Show("Failed to sync calendar", "Error",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async Task LoadGoogleSettingsAsync()
    {
        try
        {
            var settings = await _googleAuthService.GetSettingsAsync();
            if (settings != null)
            {
                GoogleClientId = settings.ClientId;
                GoogleClientSecret = settings.ClientSecret;
                ContactsSyncEnabled = settings.ContactsSyncEnabled;
                CalendarSyncEnabled = settings.CalendarSyncEnabled;
                ContactsSyncIntervalMinutes = settings.ContactsSyncIntervalMinutes;
                CalendarSyncIntervalMinutes = settings.CalendarSyncIntervalMinutes;

                if (settings.LastContactsSync.HasValue)
                    LastContactsSync = settings.LastContactsSync.Value.ToString("g");

                if (settings.LastCalendarSync.HasValue)
                    LastCalendarSync = settings.LastCalendarSync.Value.ToString("g");
            }

            await LoadGoogleAuthStatusAsync();
            await LoadGoogleDataCountsAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load Google settings");
        }
    }

    private async Task LoadGoogleAuthStatusAsync()
    {
        try
        {
            IsGoogleAuthenticated = await _googleAuthService.IsAuthenticatedAsync();

            if (IsGoogleAuthenticated)
            {
                GoogleAuthStatus = "Authenticated";
                var userProfile = await _googleAuthService.GetUserProfileAsync();
                if (userProfile != null)
                {
                    GoogleUserInfo = $"{userProfile.Name} ({userProfile.Email})";
                }
            }
            else
            {
                GoogleAuthStatus = "Not authenticated";
                GoogleUserInfo = string.Empty;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load Google auth status");
            GoogleAuthStatus = "Status unknown";
            IsGoogleAuthenticated = false;
        }
    }

    private async Task LoadGoogleDataCountsAsync()
    {
        try
        {
            if (IsGoogleAuthenticated)
            {
                ContactsCount = (await _googleContactsService.GetContactsAsync()).Count();
                CalendarEventsCount = (await _googleCalendarService.GetEventsAsync()).Count();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load Google data counts");
        }
    }

    private async Task SaveGoogleSettingsAsync()
    {
        try
        {
            var settings = await _googleAuthService.GetSettingsAsync() ?? new GoogleApiSettings();

            settings.ClientId = GoogleClientId;
            settings.ClientSecret = GoogleClientSecret;
            settings.ContactsSyncEnabled = ContactsSyncEnabled;
            settings.CalendarSyncEnabled = CalendarSyncEnabled;
            settings.ContactsSyncIntervalMinutes = ContactsSyncIntervalMinutes;
            settings.CalendarSyncIntervalMinutes = CalendarSyncIntervalMinutes;

            await _googleAuthService.UpdateSettingsAsync(settings);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to save Google settings");
        }
    }

    private string FormatSyncInterval(int minutes)
    {
        if (minutes < 60)
            return $"{minutes} minutes";
        else if (minutes == 60)
            return "1 hour";
        else if (minutes < 1440)
            return $"{minutes / 60} hours";
        else
            return $"{minutes / 1440} days";
    }
}
