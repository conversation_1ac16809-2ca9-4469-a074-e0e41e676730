using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using EmailClient.Models;
using EmailClient.Services;
using Microsoft.Extensions.Logging;
using System.Collections.ObjectModel;
using System.Windows;

namespace EmailClient.ViewModels;

public partial class SettingsViewModel : ViewModelBase
{
    private readonly ISettingsService _settingsService;
    private readonly IAccountService _accountService;
    private readonly IGoogleAuthService _googleAuthService;
    private readonly IGoogleContactsService _googleContactsService;
    private readonly IGoogleCalendarService _googleCalendarService;
    private readonly ILogger<SettingsViewModel> _logger;
    private Window? _window;

    [ObservableProperty]
    private int _syncIntervalMinutes = 15;

    [ObservableProperty]
    private bool _autoSyncEnabled = true;

    [ObservableProperty]
    private bool _syncOnStartup = true;

    [ObservableProperty]
    private int _messagesPerPage = 50;

    [ObservableProperty]
    private bool _showMessagePreview = true;

    [ObservableProperty]
    private bool _markReadOnSelect = true;

    [ObservableProperty]
    private ObservableCollection<EmailAccount> _accounts = new();

    [ObservableProperty]
    private EmailAccount? _selectedAccount;

    public bool DialogResult { get; private set; }

    public string SyncIntervalDisplay => $"{SyncIntervalMinutes} minutes";

    public SettingsViewModel(ISettingsService settingsService, IAccountService accountService, ILogger<SettingsViewModel> logger)
    {
        _settingsService = settingsService;
        _accountService = accountService;
        _logger = logger;

        _ = LoadSettingsAsync();
    }

    public void SetWindow(Window window)
    {
        _window = window;
    }

    private async Task LoadSettingsAsync()
    {
        try
        {
            // Load settings
            SyncIntervalMinutes = await _settingsService.GetIntSettingAsync("SyncIntervalMinutes", 15);
            AutoSyncEnabled = await _settingsService.GetBoolSettingAsync("AutoSyncEnabled", true);
            SyncOnStartup = await _settingsService.GetBoolSettingAsync("SyncOnStartup", true);
            MessagesPerPage = await _settingsService.GetIntSettingAsync("MessagesPerPage", 50);
            ShowMessagePreview = await _settingsService.GetBoolSettingAsync("ShowMessagePreview", true);
            MarkReadOnSelect = await _settingsService.GetBoolSettingAsync("MarkReadOnSelect", true);

            // Load accounts
            var accounts = await _accountService.GetAllAccountsAsync();
            Accounts.Clear();
            foreach (var account in accounts)
            {
                Accounts.Add(account);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load settings");
        }
    }

    partial void OnSyncIntervalMinutesChanged(int value)
    {
        OnPropertyChanged(nameof(SyncIntervalDisplay));
    }

    [RelayCommand]
    private void AddAccount()
    {
        // This would open the account setup dialog
        // For now, just show a message
        MessageBox.Show("Add account functionality - use the main menu to add accounts", "Info", MessageBoxButton.OK, MessageBoxImage.Information);
    }

    [RelayCommand]
    private void EditAccount()
    {
        if (SelectedAccount == null) return;
        
        // This would open the account edit dialog
        MessageBox.Show($"Edit account functionality for {SelectedAccount.Name}", "Info", MessageBoxButton.OK, MessageBoxImage.Information);
    }

    [RelayCommand]
    private async Task RemoveAccount()
    {
        if (SelectedAccount == null) return;

        var result = MessageBox.Show($"Are you sure you want to remove the account '{SelectedAccount.Name}'?", 
            "Confirm Remove", MessageBoxButton.YesNo, MessageBoxImage.Question);

        if (result == MessageBoxResult.Yes)
        {
            try
            {
                await _accountService.DeleteAccountAsync(SelectedAccount.Id);
                Accounts.Remove(SelectedAccount);
                SelectedAccount = null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to remove account");
                MessageBox.Show("Failed to remove account", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }

    [RelayCommand]
    private async Task Save()
    {
        try
        {
            // Save settings
            await _settingsService.SetIntSettingAsync("SyncIntervalMinutes", SyncIntervalMinutes);
            await _settingsService.SetBoolSettingAsync("AutoSyncEnabled", AutoSyncEnabled);
            await _settingsService.SetBoolSettingAsync("SyncOnStartup", SyncOnStartup);
            await _settingsService.SetIntSettingAsync("MessagesPerPage", MessagesPerPage);
            await _settingsService.SetBoolSettingAsync("ShowMessagePreview", ShowMessagePreview);
            await _settingsService.SetBoolSettingAsync("MarkReadOnSelect", MarkReadOnSelect);

            // Save account changes
            foreach (var account in Accounts)
            {
                await _accountService.UpdateAccountAsync(account);
            }

            DialogResult = true;
            _window?.Close();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to save settings");
            MessageBox.Show("Failed to save settings", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    [RelayCommand]
    private void Cancel()
    {
        DialogResult = false;
        _window?.Close();
    }
}
