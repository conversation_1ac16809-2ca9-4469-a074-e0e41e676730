using Google.Apis.Calendar.v3;
using Google.Apis.Calendar.v3.Data;
using Google.Apis.Services;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using EmailClient.Data;
using EmailClient.Models;
using System.Text.Json;

namespace EmailClient.Services;

public class GoogleCalendarService : IGoogleCalendarService
{
    private readonly EmailDbContext _context;
    private readonly IGoogleAuthService _authService;
    private readonly ILogger<GoogleCalendarService> _logger;

    public GoogleCalendarService(
        EmailDbContext context, 
        IGoogleAuthService authService, 
        ILogger<GoogleCalendarService> logger)
    {
        _context = context;
        _authService = authService;
        _logger = logger;
    }

    public async Task<int> SyncEventsAsync(bool fullSync = false)
    {
        try
        {
            if (!await IsSyncEnabledAsync())
            {
                _logger.LogInformation("Calendar sync is disabled");
                return 0;
            }

            var credential = await _authService.GetCredentialAsync();
            if (credential == null)
            {
                _logger.LogWarning("No Google authentication available for calendar sync");
                return 0;
            }

            var service = new CalendarService(new BaseClientService.Initializer()
            {
                HttpClientInitializer = credential,
                ApplicationName = "Email Client"
            });

            var syncedCount = 0;
            var settings = await _authService.GetSettingsAsync();
            
            // Get list of calendars to sync
            var calendars = await GetCalendarsAsync();
            
            foreach (var calendar in calendars.Where(c => c.Selected))
            {
                if (fullSync || string.IsNullOrEmpty(settings?.CalendarSyncToken))
                {
                    syncedCount += await PerformFullSyncAsync(service, calendar.Id);
                }
                else
                {
                    syncedCount += await PerformIncrementalSyncAsync(service, calendar.Id, settings.CalendarSyncToken);
                }
            }

            // Update last sync time
            if (settings != null)
            {
                settings.LastCalendarSync = DateTime.UtcNow;
                await _context.SaveChangesAsync();
            }

            _logger.LogInformation("Synced {Count} calendar events", syncedCount);
            return syncedCount;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to sync calendar events");
            return 0;
        }
    }

    public async Task<IEnumerable<CalendarEvent>> GetEventsAsync(DateTime? startDate = null, DateTime? endDate = null)
    {
        var query = _context.CalendarEvents.Where(e => !e.IsDeleted);

        if (startDate.HasValue)
        {
            query = query.Where(e => e.EndDateTime >= startDate.Value || (e.IsAllDay && e.StartDateTime >= startDate.Value.Date));
        }

        if (endDate.HasValue)
        {
            query = query.Where(e => e.StartDateTime <= endDate.Value || (e.IsAllDay && e.StartDateTime <= endDate.Value.Date));
        }

        return await query
            .OrderBy(e => e.StartDateTime)
            .ToListAsync();
    }

    public async Task<CalendarEvent?> GetEventAsync(int id)
    {
        return await _context.CalendarEvents
            .FirstOrDefaultAsync(e => e.Id == id && !e.IsDeleted);
    }

    public async Task<CalendarEvent?> GetEventByGoogleIdAsync(string googleId, string calendarId)
    {
        return await _context.CalendarEvents
            .FirstOrDefaultAsync(e => e.GoogleId == googleId && e.CalendarId == calendarId && !e.IsDeleted);
    }

    public async Task<IEnumerable<CalendarEvent>> GetEventsForDateAsync(DateTime date)
    {
        var startOfDay = date.Date;
        var endOfDay = startOfDay.AddDays(1);

        return await _context.CalendarEvents
            .Where(e => !e.IsDeleted &&
                ((e.StartDateTime >= startOfDay && e.StartDateTime < endOfDay) ||
                 (e.EndDateTime > startOfDay && e.EndDateTime <= endOfDay) ||
                 (e.StartDateTime < startOfDay && e.EndDateTime > endOfDay) ||
                 (e.IsAllDay && e.StartDateTime.HasValue && e.StartDateTime.Value.Date == date.Date)))
            .OrderBy(e => e.StartDateTime)
            .ToListAsync();
    }

    public async Task<IEnumerable<CalendarEvent>> GetUpcomingEventsAsync(int days = 7)
    {
        var now = DateTime.Now;
        var endDate = now.AddDays(days);

        return await _context.CalendarEvents
            .Where(e => !e.IsDeleted && 
                e.StartDateTime >= now && 
                e.StartDateTime <= endDate)
            .OrderBy(e => e.StartDateTime)
            .Take(50)
            .ToListAsync();
    }

    public async Task<IEnumerable<CalendarEvent>> SearchEventsAsync(string query)
    {
        var lowerQuery = query.ToLower();
        return await _context.CalendarEvents
            .Where(e => !e.IsDeleted && 
                (e.Summary.ToLower().Contains(lowerQuery) ||
                 e.Description.ToLower().Contains(lowerQuery) ||
                 e.Location.ToLower().Contains(lowerQuery)))
            .OrderBy(e => e.StartDateTime)
            .ToListAsync();
    }

    public async Task<CalendarEvent?> CreateEventAsync(CalendarEvent calendarEvent)
    {
        try
        {
            var credential = await _authService.GetCredentialAsync();
            if (credential == null)
                return null;

            var service = new CalendarService(new BaseClientService.Initializer()
            {
                HttpClientInitializer = credential,
                ApplicationName = "Email Client"
            });

            var googleEvent = ConvertToGoogleEvent(calendarEvent);
            var request = service.Events.Insert(googleEvent, calendarEvent.CalendarId);
            var createdEvent = await request.ExecuteAsync();

            if (createdEvent?.Id != null)
            {
                calendarEvent.GoogleId = createdEvent.Id;
                calendarEvent.ETag = createdEvent.ETag ?? string.Empty;
                calendarEvent.CreatedAt = DateTime.UtcNow;
                calendarEvent.UpdatedAt = DateTime.UtcNow;
                calendarEvent.LastSyncAt = DateTime.UtcNow;

                _context.CalendarEvents.Add(calendarEvent);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Created calendar event: {Summary}", calendarEvent.Summary);
                return calendarEvent;
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create calendar event: {Summary}", calendarEvent.Summary);
            return null;
        }
    }

    public async Task<CalendarEvent?> UpdateEventAsync(CalendarEvent calendarEvent)
    {
        try
        {
            var credential = await _authService.GetCredentialAsync();
            if (credential == null)
                return null;

            var service = new CalendarService(new BaseClientService.Initializer()
            {
                HttpClientInitializer = credential,
                ApplicationName = "Email Client"
            });

            var googleEvent = ConvertToGoogleEvent(calendarEvent);
            googleEvent.ETag = calendarEvent.ETag;

            var request = service.Events.Update(googleEvent, calendarEvent.CalendarId, calendarEvent.GoogleId);
            var updatedEvent = await request.ExecuteAsync();

            if (updatedEvent != null)
            {
                calendarEvent.ETag = updatedEvent.ETag ?? string.Empty;
                calendarEvent.UpdatedAt = DateTime.UtcNow;
                calendarEvent.LastSyncAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                _logger.LogInformation("Updated calendar event: {Summary}", calendarEvent.Summary);
                return calendarEvent;
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update calendar event: {Summary}", calendarEvent.Summary);
            return null;
        }
    }

    public async Task<bool> DeleteEventAsync(int id)
    {
        try
        {
            var calendarEvent = await GetEventAsync(id);
            if (calendarEvent == null)
                return false;

            var credential = await _authService.GetCredentialAsync();
            if (credential != null)
            {
                var service = new CalendarService(new BaseClientService.Initializer()
                {
                    HttpClientInitializer = credential,
                    ApplicationName = "Email Client"
                });

                var request = service.Events.Delete(calendarEvent.CalendarId, calendarEvent.GoogleId);
                await request.ExecuteAsync();
            }

            // Mark as deleted locally
            calendarEvent.IsDeleted = true;
            calendarEvent.UpdatedAt = DateTime.UtcNow;
            await _context.SaveChangesAsync();

            _logger.LogInformation("Deleted calendar event: {Summary}", calendarEvent.Summary);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to delete calendar event with ID: {Id}", id);
            return false;
        }
    }

    public async Task<IEnumerable<GoogleCalendarInfo>> GetCalendarsAsync()
    {
        try
        {
            var credential = await _authService.GetCredentialAsync();
            if (credential == null)
                return Enumerable.Empty<GoogleCalendarInfo>();

            var service = new CalendarService(new BaseClientService.Initializer()
            {
                HttpClientInitializer = credential,
                ApplicationName = "Email Client"
            });

            var request = service.CalendarList.List();
            var calendarList = await request.ExecuteAsync();

            if (calendarList.Items != null)
            {
                return calendarList.Items.Select(c => new GoogleCalendarInfo
                {
                    Id = c.Id ?? string.Empty,
                    Summary = c.Summary ?? string.Empty,
                    Description = c.Description ?? string.Empty,
                    TimeZone = c.TimeZone ?? string.Empty,
                    Primary = c.Primary == true,
                    AccessRole = c.AccessRole ?? string.Empty,
                    BackgroundColor = c.BackgroundColor ?? string.Empty,
                    ForegroundColor = c.ForegroundColor ?? string.Empty,
                    Selected = c.Selected == true || c.Primary == true // Select primary calendar by default
                });
            }

            return Enumerable.Empty<GoogleCalendarInfo>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get calendars");
            return Enumerable.Empty<GoogleCalendarInfo>();
        }
    }

    public async Task<IEnumerable<CalendarEvent>> GetUpdatedEventsAsync(DateTime since)
    {
        return await _context.CalendarEvents
            .Where(e => !e.IsDeleted && e.UpdatedAt > since)
            .OrderBy(e => e.UpdatedAt)
            .ToListAsync();
    }

    public async Task<DateTime?> GetLastSyncTimeAsync()
    {
        var settings = await _authService.GetSettingsAsync();
        return settings?.LastCalendarSync;
    }

    public async Task<bool> IsSyncEnabledAsync()
    {
        var settings = await _authService.GetSettingsAsync();
        return settings?.CalendarSyncEnabled == true && await _authService.IsAuthenticatedAsync();
    }

    private async Task<int> PerformFullSyncAsync(CalendarService service, string calendarId)
    {
        var request = service.Events.List(calendarId);
        request.TimeMinDateTimeOffset = DateTime.Now.AddMonths(-6); // Sync events from 6 months ago
        request.TimeMaxDateTimeOffset = DateTime.Now.AddMonths(12); // Sync events up to 1 year ahead
        request.SingleEvents = true;
        request.OrderBy = EventsResource.ListRequest.OrderByEnum.StartTime;
        request.MaxResults = 2500;

        var syncedCount = 0;
        string? nextPageToken = null;

        do
        {
            if (!string.IsNullOrEmpty(nextPageToken))
                request.PageToken = nextPageToken;

            var response = await request.ExecuteAsync();
            
            if (response.Items != null)
            {
                foreach (var googleEvent in response.Items)
                {
                    await ProcessEventAsync(googleEvent, calendarId);
                    syncedCount++;
                }
            }

            nextPageToken = response.NextPageToken;
        } while (!string.IsNullOrEmpty(nextPageToken));

        await _context.SaveChangesAsync();
        return syncedCount;
    }

    private async Task<int> PerformIncrementalSyncAsync(CalendarService service, string calendarId, string syncToken)
    {
        try
        {
            var request = service.Events.List(calendarId);
            request.SyncToken = syncToken;

            var response = await request.ExecuteAsync();
            var syncedCount = 0;

            if (response.Items != null)
            {
                foreach (var googleEvent in response.Items)
                {
                    await ProcessEventAsync(googleEvent, calendarId);
                    syncedCount++;
                }
            }

            // Update sync token
            var settings = await _authService.GetSettingsAsync();
            if (settings != null && !string.IsNullOrEmpty(response.NextSyncToken))
            {
                settings.CalendarSyncToken = response.NextSyncToken;
                await _context.SaveChangesAsync();
            }

            return syncedCount;
        }
        catch
        {
            // If incremental sync fails, fall back to full sync
            return await PerformFullSyncAsync(service, calendarId);
        }
    }

    private async Task ProcessEventAsync(Event googleEvent, string calendarId)
    {
        if (googleEvent.Id == null)
            return;

        var existingEvent = await GetEventByGoogleIdAsync(googleEvent.Id, calendarId);
        var calendarEvent = existingEvent ?? new CalendarEvent();

        // Check if event was deleted
        if (googleEvent.Status == "cancelled")
        {
            if (existingEvent != null)
            {
                existingEvent.IsDeleted = true;
                existingEvent.UpdatedAt = DateTime.UtcNow;
                existingEvent.LastSyncAt = DateTime.UtcNow;
            }
            return;
        }

        // Update event from Google data
        UpdateEventFromGoogle(calendarEvent, googleEvent, calendarId);

        if (existingEvent == null)
        {
            _context.CalendarEvents.Add(calendarEvent);
        }
        else
        {
            existingEvent.UpdatedAt = DateTime.UtcNow;
            existingEvent.LastSyncAt = DateTime.UtcNow;
        }
    }

    private void UpdateEventFromGoogle(CalendarEvent calendarEvent, Event googleEvent, string calendarId)
    {
        calendarEvent.GoogleId = googleEvent.Id ?? string.Empty;
        calendarEvent.CalendarId = calendarId;
        calendarEvent.Summary = googleEvent.Summary ?? string.Empty;
        calendarEvent.Description = googleEvent.Description ?? string.Empty;
        calendarEvent.Location = googleEvent.Location ?? string.Empty;
        calendarEvent.Status = googleEvent.Status ?? string.Empty;
        calendarEvent.Visibility = googleEvent.Visibility ?? string.Empty;
        calendarEvent.HtmlLink = googleEvent.HtmlLink ?? string.Empty;
        calendarEvent.HangoutLink = googleEvent.HangoutLink ?? string.Empty;
        calendarEvent.ETag = googleEvent.ETag ?? string.Empty;

        // Handle start and end times
        if (googleEvent.Start != null)
        {
            if (googleEvent.Start.DateTimeDateTimeOffset.HasValue)
            {
                calendarEvent.StartDateTime = googleEvent.Start.DateTimeDateTimeOffset.Value.DateTime;
                calendarEvent.IsAllDay = false;
            }
            else if (googleEvent.Start.Date != null)
            {
                calendarEvent.StartDateTime = DateTime.Parse(googleEvent.Start.Date);
                calendarEvent.IsAllDay = true;
            }
            calendarEvent.TimeZone = googleEvent.Start.TimeZone ?? string.Empty;
        }

        if (googleEvent.End != null)
        {
            if (googleEvent.End.DateTimeDateTimeOffset.HasValue)
            {
                calendarEvent.EndDateTime = googleEvent.End.DateTimeDateTimeOffset.Value.DateTime;
            }
            else if (googleEvent.End.Date != null)
            {
                calendarEvent.EndDateTime = DateTime.Parse(googleEvent.End.Date);
            }
        }

        // Handle recurrence
        if (googleEvent.Recurrence != null && googleEvent.Recurrence.Any())
        {
            calendarEvent.RecurrenceRule = string.Join(";", googleEvent.Recurrence);
        }

        calendarEvent.RecurringEventId = googleEvent.RecurringEventId ?? string.Empty;

        // Handle attendees
        if (googleEvent.Attendees != null)
        {
            var attendees = googleEvent.Attendees.Select(a => new EventAttendee
            {
                Email = a.Email ?? string.Empty,
                DisplayName = a.DisplayName ?? string.Empty,
                ResponseStatus = a.ResponseStatus ?? string.Empty,
                Optional = a.Optional == true,
                Organizer = a.Organizer == true,
                Resource = a.Resource == true
            });
            calendarEvent.Attendees = JsonSerializer.Serialize(attendees);
        }

        // Handle organizer
        if (googleEvent.Organizer != null)
        {
            var organizer = new EventOrganizer
            {
                Email = googleEvent.Organizer.Email ?? string.Empty,
                DisplayName = googleEvent.Organizer.DisplayName ?? string.Empty,
                Self = googleEvent.Organizer.Self == true
            };
            calendarEvent.Organizer = JsonSerializer.Serialize(organizer);
        }

        // Handle creator
        if (googleEvent.Creator != null)
        {
            var creator = new EventOrganizer
            {
                Email = googleEvent.Creator.Email ?? string.Empty,
                DisplayName = googleEvent.Creator.DisplayName ?? string.Empty,
                Self = googleEvent.Creator.Self == true
            };
            calendarEvent.Creator = JsonSerializer.Serialize(creator);
        }

        // Handle reminders
        if (googleEvent.Reminders != null && googleEvent.Reminders.Overrides != null)
        {
            var reminders = googleEvent.Reminders.Overrides.Select(r => new EventReminder
            {
                Method = r.Method ?? string.Empty,
                Minutes = r.Minutes ?? 0
            });
            calendarEvent.Reminders = JsonSerializer.Serialize(reminders);
        }

        // Handle conference data
        if (googleEvent.ConferenceData != null)
        {
            var conferenceInfo = new ConferenceInfo
            {
                Type = googleEvent.ConferenceData.ConferenceSolution?.Name ?? string.Empty,
                Uri = googleEvent.ConferenceData.EntryPoints?.FirstOrDefault()?.Uri ?? string.Empty,
                ConferenceId = googleEvent.ConferenceData.ConferenceId ?? string.Empty
            };
            calendarEvent.ConferenceData = JsonSerializer.Serialize(conferenceInfo);
        }

        calendarEvent.LastSyncAt = DateTime.UtcNow;
        if (calendarEvent.Id == 0)
        {
            calendarEvent.CreatedAt = DateTime.UtcNow;
        }
        calendarEvent.UpdatedAt = DateTime.UtcNow;
    }

    private Event ConvertToGoogleEvent(CalendarEvent calendarEvent)
    {
        var googleEvent = new Event
        {
            Summary = calendarEvent.Summary,
            Description = calendarEvent.Description,
            Location = calendarEvent.Location,
            Status = calendarEvent.Status,
            Visibility = calendarEvent.Visibility
        };

        // Handle start and end times
        if (calendarEvent.StartDateTime.HasValue)
        {
            if (calendarEvent.IsAllDay)
            {
                googleEvent.Start = new EventDateTime
                {
                    Date = calendarEvent.StartDateTime.Value.ToString("yyyy-MM-dd")
                };
                googleEvent.End = new EventDateTime
                {
                    Date = calendarEvent.EndDateTime?.ToString("yyyy-MM-dd") ?? calendarEvent.StartDateTime.Value.AddDays(1).ToString("yyyy-MM-dd")
                };
            }
            else
            {
                googleEvent.Start = new EventDateTime
                {
                    DateTime = calendarEvent.StartDateTime.Value,
                    TimeZone = calendarEvent.TimeZone
                };
                googleEvent.End = new EventDateTime
                {
                    DateTime = calendarEvent.EndDateTime ?? calendarEvent.StartDateTime.Value.AddHours(1),
                    TimeZone = calendarEvent.TimeZone
                };
            }
        }

        // Handle recurrence
        if (!string.IsNullOrEmpty(calendarEvent.RecurrenceRule))
        {
            googleEvent.Recurrence = calendarEvent.RecurrenceRule.Split(';').ToList();
        }

        // Handle attendees
        if (!string.IsNullOrEmpty(calendarEvent.Attendees))
        {
            try
            {
                var attendees = JsonSerializer.Deserialize<EventAttendee[]>(calendarEvent.Attendees);
                if (attendees != null)
                {
                    googleEvent.Attendees = attendees.Select(a => new EventAttendee
                    {
                        Email = a.Email,
                        DisplayName = a.DisplayName,
                        ResponseStatus = a.ResponseStatus,
                        Optional = a.Optional,
                        Organizer = a.Organizer,
                        Resource = a.Resource
                    }).ToList();
                }
            }
            catch
            {
                // Ignore JSON parsing errors
            }
        }

        return googleEvent;
    }
}
