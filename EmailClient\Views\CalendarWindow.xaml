<Window x:Class="EmailClient.Views.CalendarWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Calendar" Height="700" Width="1000"
        WindowStartupLocation="CenterOwner">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Toolbar -->
        <ToolBar Grid.Row="0">
            <Button Content="Sync Now" Command="{Binding SyncCalendarCommand}"/>
            <Separator/>
            <Button Content="Today" Command="{Binding GoToTodayCommand}"/>
            <Button Content="Previous" Command="{Binding PreviousPeriodCommand}"/>
            <Button Content="Next" Command="{Binding NextPeriodCommand}"/>
            <Separator/>
            <ComboBox SelectedItem="{Binding ViewMode}" Width="100">
                <ComboBoxItem Content="Day"/>
                <ComboBoxItem Content="Week"/>
                <ComboBoxItem Content="Month"/>
            </ComboBox>
            <Separator/>
            <TextBox x:Name="SearchBox" Width="200" 
                     Text="{Binding SearchQuery, UpdateSourceTrigger=PropertyChanged}"
                     ToolTip="Search events..."/>
            <Button Content="Search" Command="{Binding SearchCommand}"/>
        </ToolBar>
        
        <!-- Calendar Content -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="200"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            
            <!-- Sidebar -->
            <Border Grid.Column="0" BorderBrush="Gray" BorderThickness="0,0,1,0">
                <StackPanel Margin="10">
                    <TextBlock Text="Upcoming Events" FontWeight="Bold" Margin="0,0,0,10"/>
                    <ListView ItemsSource="{Binding UpcomingEvents}" MaxHeight="300">
                        <ListView.ItemTemplate>
                            <DataTemplate>
                                <StackPanel Margin="0,2">
                                    <TextBlock Text="{Binding Summary}" FontWeight="Bold"/>
                                    <TextBlock Text="{Binding StartTimeDisplay}" FontSize="10" Foreground="Gray"/>
                                </StackPanel>
                            </DataTemplate>
                        </ListView.ItemTemplate>
                    </ListView>
                    
                    <TextBlock Text="Quick Stats" FontWeight="Bold" Margin="0,20,0,10"/>
                    <TextBlock Text="{Binding EventsCountDisplay}"/>
                    <TextBlock Text="{Binding TodayEventsCountDisplay}"/>
                </StackPanel>
            </Border>
            
            <!-- Main Calendar View -->
            <ScrollViewer Grid.Column="1" VerticalScrollBarVisibility="Auto">
                <ListView ItemsSource="{Binding Events}" SelectedItem="{Binding SelectedEvent}">
                    <ListView.View>
                        <GridView>
                            <GridViewColumn Header="Title" Width="200" DisplayMemberBinding="{Binding Summary}"/>
                            <GridViewColumn Header="Start" Width="150" DisplayMemberBinding="{Binding StartTimeDisplay}"/>
                            <GridViewColumn Header="End" Width="150" DisplayMemberBinding="{Binding EndTimeDisplay}"/>
                            <GridViewColumn Header="Location" Width="150" DisplayMemberBinding="{Binding Location}"/>
                            <GridViewColumn Header="Calendar" Width="100" DisplayMemberBinding="{Binding CalendarId}"/>
                            <GridViewColumn Header="Status" Width="80" DisplayMemberBinding="{Binding Status}"/>
                        </GridView>
                    </ListView.View>
                </ListView>
            </ScrollViewer>
        </Grid>
        
        <!-- Status Bar -->
        <StatusBar Grid.Row="2">
            <StatusBarItem>
                <TextBlock Text="{Binding StatusMessage}"/>
            </StatusBarItem>
            <StatusBarItem HorizontalAlignment="Right">
                <TextBlock Text="{Binding CurrentDateDisplay}"/>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>
