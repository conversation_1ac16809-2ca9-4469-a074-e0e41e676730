using EmailClient.ViewModels;
using System.Windows;
using System.Windows.Controls;

namespace EmailClient.Views;

public partial class AccountSetupWindow : Window
{
    public AccountSetupWindow(AccountSetupViewModel viewModel)
    {
        InitializeComponent();
        DataContext = viewModel;
        
        // Set initial focus
        Loaded += (s, e) => AccountNameTextBox.Focus();
    }

    private void PasswordBox_PasswordChanged(object sender, RoutedEventArgs e)
    {
        if (DataContext is AccountSetupViewModel viewModel && sender is PasswordBox passwordBox)
        {
            viewModel.Password = passwordBox.Password;
        }
    }
}
