using Google.Apis.Auth.OAuth2;
using Google.Apis.Auth.OAuth2.Flows;
using Google.Apis.Auth.OAuth2.Responses;
using Google.Apis.Oauth2.v2;
using Google.Apis.Oauth2.v2.Data;
using Google.Apis.Services;
using Google.Apis.Util.Store;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using EmailClient.Data;
using EmailClient.Models;
using System.Text.Json;

namespace EmailClient.Services;

public class GoogleAuthService : IGoogleAuthService
{
    private readonly EmailDbContext _context;
    private readonly ILogger<GoogleAuthService> _logger;
    private UserCredential? _credential;
    private GoogleApiSettings? _settings;

    public GoogleAuthService(EmailDbContext context, ILogger<GoogleAuthService> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<bool> AuthenticateAsync(string clientId, string clientSecret, string[] scopes)
    {
        try
        {
            var clientSecrets = new ClientSecrets
            {
                ClientId = clientId,
                ClientSecret = clientSecret
            };

            // Use a custom data store to save tokens to our database
            var dataStore = new DatabaseTokenStore(_context);

            _credential = await GoogleWebAuthorizationBroker.AuthorizeAsync(
                clientSecrets,
                scopes,
                "user",
                CancellationToken.None,
                dataStore);

            if (_credential != null)
            {
                // Save or update settings
                await SaveSettingsAsync(clientId, clientSecret, scopes, _credential);
                
                _logger.LogInformation("Google authentication successful");
                return true;
            }

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to authenticate with Google");
            return false;
        }
    }

    public async Task<bool> IsAuthenticatedAsync()
    {
        try
        {
            var settings = await GetSettingsAsync();
            if (settings == null || !settings.IsAuthenticated)
                return false;

            if (_credential == null)
            {
                await LoadCredentialFromSettingsAsync();
            }

            if (_credential?.Token?.IsExpired(Google.Apis.Util.SystemClock.Default) == true)
            {
                return await RefreshTokenAsync();
            }

            return _credential != null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking authentication status");
            return false;
        }
    }

    public async Task<UserCredential?> GetCredentialAsync()
    {
        if (await IsAuthenticatedAsync())
        {
            return _credential;
        }
        return null;
    }

    public async Task<bool> RefreshTokenAsync()
    {
        try
        {
            if (_credential?.Token != null)
            {
                var success = await _credential.RefreshTokenAsync(CancellationToken.None);
                if (success)
                {
                    // Update the stored token
                    await UpdateStoredTokenAsync(_credential.Token);
                    _logger.LogInformation("Google token refreshed successfully");
                    return true;
                }
            }
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to refresh Google token");
            return false;
        }
    }

    public async Task RevokeAuthenticationAsync()
    {
        try
        {
            if (_credential != null)
            {
                await _credential.RevokeTokenAsync(CancellationToken.None);
            }

            // Clear stored settings
            var settings = await GetSettingsAsync();
            if (settings != null)
            {
                settings.IsAuthenticated = false;
                settings.AccessToken = string.Empty;
                settings.RefreshToken = string.Empty;
                settings.TokenExpiresAt = null;
                settings.UpdatedAt = DateTime.UtcNow;
                
                await _context.SaveChangesAsync();
            }

            _credential = null;
            _settings = null;

            _logger.LogInformation("Google authentication revoked");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error revoking Google authentication");
        }
    }

    public async Task<GoogleApiSettings?> GetSettingsAsync()
    {
        if (_settings == null)
        {
            _settings = await _context.GoogleApiSettings.FirstOrDefaultAsync();
        }
        return _settings;
    }

    public async Task UpdateSettingsAsync(GoogleApiSettings settings)
    {
        var existing = await _context.GoogleApiSettings.FirstOrDefaultAsync();
        if (existing != null)
        {
            existing.ClientId = settings.ClientId;
            existing.ClientSecret = settings.ClientSecret;
            existing.ContactsSyncEnabled = settings.ContactsSyncEnabled;
            existing.CalendarSyncEnabled = settings.CalendarSyncEnabled;
            existing.ContactsSyncIntervalMinutes = settings.ContactsSyncIntervalMinutes;
            existing.CalendarSyncIntervalMinutes = settings.CalendarSyncIntervalMinutes;
            existing.UpdatedAt = DateTime.UtcNow;
        }
        else
        {
            _context.GoogleApiSettings.Add(settings);
        }

        await _context.SaveChangesAsync();
        _settings = existing ?? settings;
    }

    public async Task<GoogleUserProfile?> GetUserProfileAsync()
    {
        try
        {
            var credential = await GetCredentialAsync();
            if (credential == null)
                return null;

            var service = new Oauth2Service(new BaseClientService.Initializer()
            {
                HttpClientInitializer = credential,
                ApplicationName = "Email Client"
            });

            var userInfo = await service.Userinfo.Get().ExecuteAsync();
            
            return new GoogleUserProfile
            {
                Id = userInfo.Id ?? string.Empty,
                Email = userInfo.Email ?? string.Empty,
                Name = userInfo.Name ?? string.Empty,
                GivenName = userInfo.GivenName ?? string.Empty,
                FamilyName = userInfo.FamilyName ?? string.Empty,
                Picture = userInfo.Picture ?? string.Empty,
                VerifiedEmail = userInfo.VerifiedEmail ?? false
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get user profile");
            return null;
        }
    }

    private async Task SaveSettingsAsync(string clientId, string clientSecret, string[] scopes, UserCredential credential)
    {
        var settings = await GetSettingsAsync() ?? new GoogleApiSettings();
        
        settings.ClientId = clientId;
        settings.ClientSecret = clientSecret; // Should be encrypted in production
        settings.AccessToken = credential.Token.AccessToken; // Should be encrypted in production
        settings.RefreshToken = credential.Token.RefreshToken ?? string.Empty; // Should be encrypted in production
        settings.TokenExpiresAt = credential.Token.IssuedUtc?.AddSeconds(credential.Token.ExpiresInSeconds ?? 3600);
        settings.Scopes = JsonSerializer.Serialize(scopes);
        settings.IsAuthenticated = true;
        settings.UpdatedAt = DateTime.UtcNow;

        if (settings.Id == 0)
        {
            _context.GoogleApiSettings.Add(settings);
        }

        await _context.SaveChangesAsync();
        _settings = settings;
    }

    private async Task LoadCredentialFromSettingsAsync()
    {
        var settings = await GetSettingsAsync();
        if (settings == null || !settings.IsAuthenticated)
            return;

        try
        {
            var token = new TokenResponse
            {
                AccessToken = settings.AccessToken,
                RefreshToken = settings.RefreshToken,
                ExpiresInSeconds = settings.TokenExpiresAt.HasValue 
                    ? (long)(settings.TokenExpiresAt.Value - DateTime.UtcNow).TotalSeconds 
                    : 3600,
                IssuedUtc = settings.TokenExpiresAt?.AddSeconds(-3600) ?? DateTime.UtcNow.AddHours(-1)
            };

            var flow = new GoogleAuthorizationCodeFlow(new GoogleAuthorizationCodeFlow.Initializer
            {
                ClientSecrets = new ClientSecrets
                {
                    ClientId = settings.ClientId,
                    ClientSecret = settings.ClientSecret
                },
                Scopes = JsonSerializer.Deserialize<string[]>(settings.Scopes) ?? GoogleScopes.DefaultScopes
            });

            _credential = new UserCredential(flow, "user", token);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load credential from settings");
        }
    }

    private async Task UpdateStoredTokenAsync(TokenResponse token)
    {
        var settings = await GetSettingsAsync();
        if (settings != null)
        {
            settings.AccessToken = token.AccessToken;
            settings.RefreshToken = token.RefreshToken ?? settings.RefreshToken;
            settings.TokenExpiresAt = token.IssuedUtc?.AddSeconds(token.ExpiresInSeconds ?? 3600);
            settings.UpdatedAt = DateTime.UtcNow;
            
            await _context.SaveChangesAsync();
        }
    }
}

// Custom token store that saves tokens to our database
internal class DatabaseTokenStore : IDataStore
{
    private readonly EmailDbContext _context;

    public DatabaseTokenStore(EmailDbContext context)
    {
        _context = context;
    }

    public async Task StoreAsync<T>(string key, T value)
    {
        var settings = await _context.GoogleApiSettings.FirstOrDefaultAsync();
        if (settings != null && value is TokenResponse token)
        {
            settings.AccessToken = token.AccessToken;
            settings.RefreshToken = token.RefreshToken ?? settings.RefreshToken;
            settings.TokenExpiresAt = token.IssuedUtc?.AddSeconds(token.ExpiresInSeconds ?? 3600);
            settings.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();
        }
    }

    public async Task DeleteAsync<T>(string key)
    {
        var settings = await _context.GoogleApiSettings.FirstOrDefaultAsync();
        if (settings != null)
        {
            settings.AccessToken = string.Empty;
            settings.RefreshToken = string.Empty;
            settings.TokenExpiresAt = null;
            settings.IsAuthenticated = false;
            settings.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();
        }
    }

    public async Task<T?> GetAsync<T>(string key)
    {
        var settings = await _context.GoogleApiSettings.FirstOrDefaultAsync();
        if (settings != null && !string.IsNullOrEmpty(settings.AccessToken))
        {
            var token = new TokenResponse
            {
                AccessToken = settings.AccessToken,
                RefreshToken = settings.RefreshToken,
                ExpiresInSeconds = settings.TokenExpiresAt.HasValue
                    ? (long)(settings.TokenExpiresAt.Value - DateTime.UtcNow).TotalSeconds
                    : 3600,
                IssuedUtc = settings.TokenExpiresAt?.AddSeconds(-3600) ?? DateTime.UtcNow.AddHours(-1)
            };

            return (T)(object)token;
        }

        return default(T);
    }

    public Task ClearAsync()
    {
        return DeleteAsync<TokenResponse>("user");
    }
}
