using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
using EmailClient.Models;

namespace EmailClient.Services;

public class SyncService : ISyncService, IDisposable
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ISettingsService _settingsService;
    private readonly ILogger<SyncService> _logger;
    private Timer? _syncTimer;
    private bool _isRunning;
    private bool _isSyncing;

    public bool IsRunning => _isRunning;
    public event EventHandler<SyncCompletedEventArgs>? SyncCompleted;

    public SyncService(IServiceProvider serviceProvider, ISettingsService settingsService, ILogger<SyncService> logger)
    {
        _serviceProvider = serviceProvider;
        _settingsService = settingsService;
        _logger = logger;
    }

    public async Task StartAsync()
    {
        if (_isRunning) return;

        _logger.LogInformation("Starting sync service");
        
        var syncIntervalMinutes = await _settingsService.GetIntSettingAsync("SyncIntervalMinutes", 15);
        var syncInterval = TimeSpan.FromMinutes(syncIntervalMinutes);

        _syncTimer = new Timer(async _ => await SyncCallback(), null, TimeSpan.Zero, syncInterval);
        _isRunning = true;
    }

    public async Task StopAsync()
    {
        if (!_isRunning) return;

        _logger.LogInformation("Stopping sync service");
        
        _syncTimer?.Dispose();
        _syncTimer = null;
        _isRunning = false;

        // Wait for any ongoing sync to complete
        while (_isSyncing)
        {
            await Task.Delay(100);
        }
    }

    public async Task SyncNowAsync()
    {
        if (_isSyncing)
        {
            _logger.LogInformation("Sync already in progress, skipping");
            return;
        }

        await SyncCallback();
    }

    private async Task SyncCallback()
    {
        if (_isSyncing) return;

        _isSyncing = true;
        var eventArgs = new SyncCompletedEventArgs();

        try
        {
            _logger.LogInformation("Starting background sync");

            // Create a new scope for this background operation
            using var scope = _serviceProvider.CreateScope();

            // Sync email accounts
            var emailService = scope.ServiceProvider.GetRequiredService<IEmailService>();
            await emailService.SyncAllAccountsAsync();

            // Sync Google data if enabled and authenticated
            await SyncGoogleDataAsync(scope);

            eventArgs.Success = true;
            eventArgs.AccountsSynced = 1; // This would be calculated properly in a real implementation
            eventArgs.MessagesSynced = 0; // This would be calculated properly in a real implementation

            _logger.LogInformation("Background sync completed successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Background sync failed");
            eventArgs.Success = false;
            eventArgs.ErrorMessage = ex.Message;
        }
        finally
        {
            _isSyncing = false;
            SyncCompleted?.Invoke(this, eventArgs);
        }
    }

    private async Task SyncGoogleDataAsync(IServiceScope scope)
    {
        try
        {
            var googleAuthService = scope.ServiceProvider.GetRequiredService<IGoogleAuthService>();

            if (!await googleAuthService.IsAuthenticatedAsync())
            {
                _logger.LogDebug("Google authentication not available, skipping Google sync");
                return;
            }

            var settings = await googleAuthService.GetSettingsAsync();
            if (settings == null)
            {
                _logger.LogDebug("Google settings not found, skipping Google sync");
                return;
            }

            // Sync contacts if enabled and due
            if (settings.ContactsSyncEnabled && ShouldSyncContacts(settings))
            {
                var contactsService = scope.ServiceProvider.GetRequiredService<IGoogleContactsService>();
                var contactsCount = await contactsService.SyncContactsAsync();
                _logger.LogInformation("Synced {Count} contacts from Google", contactsCount);
            }

            // Sync calendar if enabled and due
            if (settings.CalendarSyncEnabled && ShouldSyncCalendar(settings))
            {
                var calendarService = scope.ServiceProvider.GetRequiredService<IGoogleCalendarService>();
                var eventsCount = await calendarService.SyncEventsAsync();
                _logger.LogInformation("Synced {Count} calendar events from Google", eventsCount);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to sync Google data");
        }
    }

    private bool ShouldSyncContacts(GoogleApiSettings settings)
    {
        if (!settings.LastContactsSync.HasValue)
            return true;

        var nextSyncTime = settings.LastContactsSync.Value.AddMinutes(settings.ContactsSyncIntervalMinutes);
        return DateTime.UtcNow >= nextSyncTime;
    }

    private bool ShouldSyncCalendar(GoogleApiSettings settings)
    {
        if (!settings.LastCalendarSync.HasValue)
            return true;

        var nextSyncTime = settings.LastCalendarSync.Value.AddMinutes(settings.CalendarSyncIntervalMinutes);
        return DateTime.UtcNow >= nextSyncTime;
    }

    public void Dispose()
    {
        _syncTimer?.Dispose();
    }
}
