using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;

namespace EmailClient.Services;

public class SyncService : ISyncService, IDisposable
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ISettingsService _settingsService;
    private readonly ILogger<SyncService> _logger;
    private Timer? _syncTimer;
    private bool _isRunning;
    private bool _isSyncing;

    public bool IsRunning => _isRunning;
    public event EventHandler<SyncCompletedEventArgs>? SyncCompleted;

    public SyncService(IServiceProvider serviceProvider, ISettingsService settingsService, ILogger<SyncService> logger)
    {
        _serviceProvider = serviceProvider;
        _settingsService = settingsService;
        _logger = logger;
    }

    public async Task StartAsync()
    {
        if (_isRunning) return;

        _logger.LogInformation("Starting sync service");
        
        var syncIntervalMinutes = await _settingsService.GetIntSettingAsync("SyncIntervalMinutes", 15);
        var syncInterval = TimeSpan.FromMinutes(syncIntervalMinutes);

        _syncTimer = new Timer(async _ => await SyncCallback(), null, TimeSpan.Zero, syncInterval);
        _isRunning = true;
    }

    public async Task StopAsync()
    {
        if (!_isRunning) return;

        _logger.LogInformation("Stopping sync service");
        
        _syncTimer?.Dispose();
        _syncTimer = null;
        _isRunning = false;

        // Wait for any ongoing sync to complete
        while (_isSyncing)
        {
            await Task.Delay(100);
        }
    }

    public async Task SyncNowAsync()
    {
        if (_isSyncing)
        {
            _logger.LogInformation("Sync already in progress, skipping");
            return;
        }

        await SyncCallback();
    }

    private async Task SyncCallback()
    {
        if (_isSyncing) return;

        _isSyncing = true;
        var eventArgs = new SyncCompletedEventArgs();

        try
        {
            _logger.LogInformation("Starting background sync");

            // Create a new scope for this background operation
            using var scope = _serviceProvider.CreateScope();
            var emailService = scope.ServiceProvider.GetRequiredService<IEmailService>();
            await emailService.SyncAllAccountsAsync();

            eventArgs.Success = true;
            eventArgs.AccountsSynced = 1; // This would be calculated properly in a real implementation
            eventArgs.MessagesSynced = 0; // This would be calculated properly in a real implementation

            _logger.LogInformation("Background sync completed successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Background sync failed");
            eventArgs.Success = false;
            eventArgs.ErrorMessage = ex.Message;
        }
        finally
        {
            _isSyncing = false;
            SyncCompleted?.Invoke(this, eventArgs);
        }
    }

    public void Dispose()
    {
        _syncTimer?.Dispose();
    }
}
