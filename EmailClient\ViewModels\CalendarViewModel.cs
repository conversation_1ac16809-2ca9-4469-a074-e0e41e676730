using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using EmailClient.Models;
using EmailClient.Services;
using Microsoft.Extensions.Logging;
using System.Collections.ObjectModel;
using System.Windows;

namespace EmailClient.ViewModels;

public partial class CalendarViewModel : ViewModelBase
{
    private readonly IGoogleCalendarService _calendarService;
    private readonly ILogger<CalendarViewModel> _logger;

    [ObservableProperty]
    private ObservableCollection<CalendarEventViewModel> _events = new();

    [ObservableProperty]
    private ObservableCollection<CalendarEventViewModel> _upcomingEvents = new();

    [ObservableProperty]
    private CalendarEventViewModel? _selectedEvent;

    [ObservableProperty]
    private string _searchQuery = string.Empty;

    [ObservableProperty]
    private string _statusMessage = "Ready";

    [ObservableProperty]
    private string _viewMode = "Month";

    [ObservableProperty]
    private DateTime _currentDate = DateTime.Today;

    [ObservableProperty]
    private int _eventsCount = 0;

    [ObservableProperty]
    private int _todayEventsCount = 0;

    public string EventsCountDisplay => $"{EventsCount} events";
    public string TodayEventsCountDisplay => $"{TodayEventsCount} today";
    public string CurrentDateDisplay => CurrentDate.ToString("MMMM yyyy");

    public CalendarViewModel(IGoogleCalendarService calendarService, ILogger<CalendarViewModel> logger)
    {
        _calendarService = calendarService;
        _logger = logger;

        _ = LoadEventsAsync();
    }

    [RelayCommand]
    private async Task SyncCalendar()
    {
        try
        {
            StatusMessage = "Syncing calendar...";
            var count = await _calendarService.SyncEventsAsync(fullSync: true);
            StatusMessage = $"Synced {count} events";
            
            await LoadEventsAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to sync calendar");
            StatusMessage = "Failed to sync calendar";
            MessageBox.Show("Failed to sync calendar", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    [RelayCommand]
    private async Task Search()
    {
        try
        {
            if (string.IsNullOrWhiteSpace(SearchQuery))
            {
                await LoadEventsAsync();
                return;
            }

            StatusMessage = "Searching...";
            var searchResults = await _calendarService.SearchEventsAsync(SearchQuery);
            
            Events.Clear();
            foreach (var calendarEvent in searchResults)
            {
                Events.Add(new CalendarEventViewModel(calendarEvent));
            }

            EventsCount = Events.Count;
            StatusMessage = $"Found {EventsCount} events";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to search events");
            StatusMessage = "Search failed";
        }
    }

    [RelayCommand]
    private void GoToToday()
    {
        CurrentDate = DateTime.Today;
        _ = LoadEventsAsync();
    }

    [RelayCommand]
    private void PreviousPeriod()
    {
        CurrentDate = ViewMode switch
        {
            "Day" => CurrentDate.AddDays(-1),
            "Week" => CurrentDate.AddDays(-7),
            "Month" => CurrentDate.AddMonths(-1),
            _ => CurrentDate.AddMonths(-1)
        };
        _ = LoadEventsAsync();
    }

    [RelayCommand]
    private void NextPeriod()
    {
        CurrentDate = ViewMode switch
        {
            "Day" => CurrentDate.AddDays(1),
            "Week" => CurrentDate.AddDays(7),
            "Month" => CurrentDate.AddMonths(1),
            _ => CurrentDate.AddMonths(1)
        };
        _ = LoadEventsAsync();
    }

    private async Task LoadEventsAsync()
    {
        try
        {
            StatusMessage = "Loading events...";
            
            // Load events for current period
            var (startDate, endDate) = GetDateRange();
            var events = await _calendarService.GetEventsAsync(startDate, endDate);
            
            Events.Clear();
            foreach (var calendarEvent in events)
            {
                Events.Add(new CalendarEventViewModel(calendarEvent));
            }

            // Load upcoming events
            var upcomingEvents = await _calendarService.GetUpcomingEventsAsync(7);
            UpcomingEvents.Clear();
            foreach (var calendarEvent in upcomingEvents.Take(10))
            {
                UpcomingEvents.Add(new CalendarEventViewModel(calendarEvent));
            }

            // Load today's events count
            var todayEvents = await _calendarService.GetEventsForDateAsync(DateTime.Today);
            TodayEventsCount = todayEvents.Count();

            EventsCount = Events.Count;
            StatusMessage = "Ready";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load events");
            StatusMessage = "Failed to load events";
        }
    }

    private (DateTime startDate, DateTime endDate) GetDateRange()
    {
        return ViewMode switch
        {
            "Day" => (CurrentDate.Date, CurrentDate.Date.AddDays(1)),
            "Week" => GetWeekRange(CurrentDate),
            "Month" => GetMonthRange(CurrentDate),
            _ => GetMonthRange(CurrentDate)
        };
    }

    private (DateTime startDate, DateTime endDate) GetWeekRange(DateTime date)
    {
        var startOfWeek = date.AddDays(-(int)date.DayOfWeek);
        return (startOfWeek, startOfWeek.AddDays(7));
    }

    private (DateTime startDate, DateTime endDate) GetMonthRange(DateTime date)
    {
        var startOfMonth = new DateTime(date.Year, date.Month, 1);
        var endOfMonth = startOfMonth.AddMonths(1);
        return (startOfMonth, endOfMonth);
    }

    partial void OnCurrentDateChanged(DateTime value)
    {
        OnPropertyChanged(nameof(CurrentDateDisplay));
    }

    partial void OnViewModeChanged(string value)
    {
        _ = LoadEventsAsync();
    }

    partial void OnSearchQueryChanged(string value)
    {
        if (string.IsNullOrWhiteSpace(value))
        {
            _ = LoadEventsAsync();
        }
    }
}

public class CalendarEventViewModel
{
    private readonly CalendarEvent _event;

    public CalendarEventViewModel(CalendarEvent calendarEvent)
    {
        _event = calendarEvent;
    }

    public int Id => _event.Id;
    public string Summary => _event.Summary;
    public string Description => _event.Description;
    public string Location => _event.Location;
    public string CalendarId => _event.CalendarId;
    public string Status => _event.Status;
    public DateTime? StartDateTime => _event.StartDateTime;
    public DateTime? EndDateTime => _event.EndDateTime;
    public bool IsAllDay => _event.IsAllDay;

    public string StartTimeDisplay
    {
        get
        {
            if (!StartDateTime.HasValue)
                return string.Empty;

            return IsAllDay 
                ? StartDateTime.Value.ToString("MMM dd, yyyy") 
                : StartDateTime.Value.ToString("MMM dd, yyyy h:mm tt");
        }
    }

    public string EndTimeDisplay
    {
        get
        {
            if (!EndDateTime.HasValue)
                return string.Empty;

            return IsAllDay 
                ? EndDateTime.Value.ToString("MMM dd, yyyy") 
                : EndDateTime.Value.ToString("MMM dd, yyyy h:mm tt");
        }
    }
}
