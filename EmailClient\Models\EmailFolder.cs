using System.ComponentModel.DataAnnotations;

namespace EmailClient.Models;

public class EmailFolder
{
    public int Id { get; set; }
    
    public int AccountId { get; set; }
    public virtual EmailAccount Account { get; set; } = null!;
    
    [Required]
    public string Name { get; set; } = string.Empty;
    
    [Required]
    public string FullName { get; set; } = string.Empty; // IMAP folder path
    
    public int? ParentFolderId { get; set; }
    public virtual EmailFolder? ParentFolder { get; set; }
    
    public FolderType Type { get; set; } = FolderType.Custom;
    
    public int UnreadCount { get; set; } = 0;
    
    public int TotalCount { get; set; } = 0;
    
    public DateTime LastSyncAt { get; set; }
    
    // Navigation properties
    public virtual ICollection<EmailFolder> SubFolders { get; set; } = new List<EmailFolder>();
    public virtual ICollection<EmailMessage> Messages { get; set; } = new List<EmailMessage>();
}

public enum FolderType
{
    Inbox,
    Sent,
    Drafts,
    Trash,
    Spam,
    Archive,
    Custom
}
