namespace EmailClient.Services;

public interface ISyncService
{
    Task StartAsync();
    Task StopAsync();
    Task SyncNowAsync();
    bool IsRunning { get; }
    event EventHandler<SyncCompletedEventArgs>? SyncCompleted;
}

public class SyncCompletedEventArgs : EventArgs
{
    public bool Success { get; set; }
    public string? ErrorMessage { get; set; }
    public int AccountsSynced { get; set; }
    public int MessagesSynced { get; set; }
}
