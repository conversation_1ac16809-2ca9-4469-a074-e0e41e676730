using EmailClient.Models;

namespace EmailClient.Services;

public interface IGoogleCalendarService
{
    /// <summary>
    /// Synchronizes calendar events from Google Calendar API
    /// </summary>
    /// <param name="fullSync">If true, performs full sync; if false, performs incremental sync</param>
    /// <returns>Number of events synchronized</returns>
    Task<int> SyncEventsAsync(bool fullSync = false);
    
    /// <summary>
    /// Gets all calendar events from local database
    /// </summary>
    /// <param name="startDate">Optional start date filter</param>
    /// <param name="endDate">Optional end date filter</param>
    /// <returns>List of calendar events</returns>
    Task<IEnumerable<CalendarEvent>> GetEventsAsync(DateTime? startDate = null, DateTime? endDate = null);
    
    /// <summary>
    /// Gets a specific calendar event by ID
    /// </summary>
    /// <param name="id">Event ID</param>
    /// <returns>Calendar event if found, null otherwise</returns>
    Task<CalendarEvent?> GetEventAsync(int id);
    
    /// <summary>
    /// Gets a calendar event by Google ID
    /// </summary>
    /// <param name="googleId">Google event ID</param>
    /// <param name="calendarId">Google calendar ID</param>
    /// <returns>Calendar event if found, null otherwise</returns>
    Task<CalendarEvent?> GetEventByGoogleIdAsync(string googleId, string calendarId);
    
    /// <summary>
    /// Gets events for a specific date
    /// </summary>
    /// <param name="date">Date to get events for</param>
    /// <returns>Events for the specified date</returns>
    Task<IEnumerable<CalendarEvent>> GetEventsForDateAsync(DateTime date);
    
    /// <summary>
    /// Gets upcoming events within the next specified days
    /// </summary>
    /// <param name="days">Number of days to look ahead</param>
    /// <returns>Upcoming events</returns>
    Task<IEnumerable<CalendarEvent>> GetUpcomingEventsAsync(int days = 7);
    
    /// <summary>
    /// Searches calendar events by title or description
    /// </summary>
    /// <param name="query">Search query</param>
    /// <returns>Matching events</returns>
    Task<IEnumerable<CalendarEvent>> SearchEventsAsync(string query);
    
    /// <summary>
    /// Creates a new calendar event in Google and syncs to local database
    /// </summary>
    /// <param name="calendarEvent">Event to create</param>
    /// <returns>Created event with Google ID</returns>
    Task<CalendarEvent?> CreateEventAsync(CalendarEvent calendarEvent);
    
    /// <summary>
    /// Updates an existing calendar event in Google and local database
    /// </summary>
    /// <param name="calendarEvent">Event to update</param>
    /// <returns>Updated event</returns>
    Task<CalendarEvent?> UpdateEventAsync(CalendarEvent calendarEvent);
    
    /// <summary>
    /// Deletes a calendar event from Google and local database
    /// </summary>
    /// <param name="id">Event ID</param>
    /// <returns>True if deleted successfully</returns>
    Task<bool> DeleteEventAsync(int id);
    
    /// <summary>
    /// Gets available calendars from Google
    /// </summary>
    /// <returns>List of available calendars</returns>
    Task<IEnumerable<GoogleCalendarInfo>> GetCalendarsAsync();
    
    /// <summary>
    /// Gets events that have been updated since the last sync
    /// </summary>
    /// <param name="since">Date to check for updates since</param>
    /// <returns>Updated events</returns>
    Task<IEnumerable<CalendarEvent>> GetUpdatedEventsAsync(DateTime since);
    
    /// <summary>
    /// Gets the last sync timestamp
    /// </summary>
    /// <returns>Last sync timestamp, null if never synced</returns>
    Task<DateTime?> GetLastSyncTimeAsync();
    
    /// <summary>
    /// Checks if calendar sync is enabled
    /// </summary>
    /// <returns>True if enabled</returns>
    Task<bool> IsSyncEnabledAsync();
}

public class GoogleCalendarInfo
{
    public string Id { get; set; } = string.Empty;
    public string Summary { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string TimeZone { get; set; } = string.Empty;
    public bool Primary { get; set; } = false;
    public string AccessRole { get; set; } = string.Empty;
    public string BackgroundColor { get; set; } = string.Empty;
    public string ForegroundColor { get; set; } = string.Empty;
    public bool Selected { get; set; } = false;
}
