<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:local="clr-namespace:EmailClient.Converters">

    <!-- Converters -->
    <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
    <local:NullToVisibilityConverter x:Key="NullToVisibilityConverter"/>
    <local:InvertedNullToVisibilityConverter x:Key="InvertedNullToVisibilityConverter"/>

    <!-- Button Styles -->
    <Style TargetType="Button">
        <Setter Property="Padding" Value="8,4"/>
        <Setter Property="Margin" Value="2"/>
        <Setter Property="MinWidth" Value="75"/>
    </Style>

    <!-- TextBox Styles -->
    <Style TargetType="TextBox">
        <Setter Property="Padding" Value="4"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
    </Style>

    <!-- ListView Styles -->
    <Style TargetType="ListView">
        <Setter Property="GridViewColumnHeader.Background" Value="LightGray"/>
        <Setter Property="GridViewColumnHeader.BorderBrush" Value="Gray"/>
        <Setter Property="GridViewColumnHeader.BorderThickness" Value="0,0,1,1"/>
    </Style>

    <!-- TreeView Styles -->
    <Style TargetType="TreeView">
        <Setter Property="Background" Value="White"/>
        <Setter Property="BorderBrush" Value="LightGray"/>
        <Setter Property="BorderThickness" Value="1"/>
    </Style>

    <!-- StatusBar Styles -->
    <Style TargetType="StatusBar">
        <Setter Property="Background" Value="LightGray"/>
        <Setter Property="BorderBrush" Value="Gray"/>
        <Setter Property="BorderThickness" Value="0,1,0,0"/>
    </Style>

</ResourceDictionary>
