using EmailClient.Models;
using System.Collections.ObjectModel;
using System.Windows;

namespace EmailClient.ViewModels;

public class FolderTreeItemViewModel : ViewModelBase
{
    public string Name { get; set; } = string.Empty;
    public string FullName { get; set; } = string.Empty;
    public FolderType Type { get; set; }
    public int UnreadCount { get; set; }
    public int TotalCount { get; set; }
    public int? FolderId { get; set; }
    public int? AccountId { get; set; }
    public bool IsAccount { get; set; }
    
    public ObservableCollection<FolderTreeItemViewModel> Children { get; set; } = new();

    public string UnreadCountDisplay => UnreadCount > 0 ? $"({UnreadCount})" : string.Empty;
    public bool HasUnread => UnreadCount > 0;
    public FontWeight FontWeight => HasUnread ? FontWeights.Bold : FontWeights.Normal;

    public static FolderTreeItemViewModel FromAccount(EmailAccount account)
    {
        return new FolderTreeItemViewModel
        {
            Name = account.Name,
            FullName = account.EmailAddress,
            AccountId = account.Id,
            IsAccount = true,
            Type = FolderType.Custom
        };
    }

    public static FolderTreeItemViewModel FromFolder(EmailFolder folder)
    {
        return new FolderTreeItemViewModel
        {
            Name = folder.Name,
            FullName = folder.FullName,
            Type = folder.Type,
            UnreadCount = folder.UnreadCount,
            TotalCount = folder.TotalCount,
            FolderId = folder.Id,
            AccountId = folder.AccountId,
            IsAccount = false
        };
    }
}
