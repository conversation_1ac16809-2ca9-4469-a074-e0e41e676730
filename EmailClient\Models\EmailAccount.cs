using System.ComponentModel.DataAnnotations;

namespace EmailClient.Models;

public class EmailAccount
{
    public int Id { get; set; }
    
    [Required]
    public string Name { get; set; } = string.Empty;
    
    [Required]
    [EmailAddress]
    public string EmailAddress { get; set; } = string.Empty;
    
    [Required]
    public string ImapServer { get; set; } = string.Empty;
    
    public int ImapPort { get; set; } = 993;
    
    public bool UseSsl { get; set; } = true;
    
    [Required]
    public string Username { get; set; } = string.Empty;
    
    [Required]
    public string Password { get; set; } = string.Empty; // In production, this should be encrypted
    
    public bool IsEnabled { get; set; } = true;
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime LastSyncAt { get; set; }
    
    // Navigation properties
    public virtual ICollection<EmailMessage> Messages { get; set; } = new List<EmailMessage>();
    public virtual ICollection<EmailFolder> Folders { get; set; } = new List<EmailFolder>();
}
