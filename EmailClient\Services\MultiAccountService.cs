using EmailClient.Data;
using EmailClient.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Diagnostics;

namespace EmailClient.Services;

public class MultiAccountService : IMultiAccountService
{
    private readonly EmailDbContext _context;
    private readonly IImapService _imapService;
    private readonly IEmailService _emailService;
    private readonly ILogger<MultiAccountService> _logger;

    public MultiAccountService(
        EmailDbContext context, 
        IImapService imapService, 
        IEmailService emailService,
        ILogger<MultiAccountService> logger)
    {
        _context = context;
        _imapService = imapService;
        _emailService = emailService;
        _logger = logger;
    }

    public async Task<UnifiedInboxResult> GetUnifiedInboxAsync(int pageSize = 50, int pageNumber = 1)
    {
        var skip = (pageNumber - 1) * pageSize;
        
        // Get all inbox messages across accounts
        var inboxQuery = _context.Messages
            .Include(m => m.Account)
            .Include(m => m.Folder)
            .Where(m => !m.IsDeleted && m.Folder!.Type == FolderType.Inbox)
            .OrderByDescending(m => m.DateReceived);

        var totalCount = await inboxQuery.CountAsync();
        var unreadCount = await inboxQuery.CountAsync(m => !m.IsRead);
        
        var messages = await inboxQuery
            .Skip(skip)
            .Take(pageSize)
            .ToListAsync();

        // Get message count by account
        var messageCountByAccount = await _context.Messages
            .Include(m => m.Folder)
            .Where(m => !m.IsDeleted && m.Folder!.Type == FolderType.Inbox)
            .GroupBy(m => m.AccountId)
            .ToDictionaryAsync(g => g.Key, g => g.Count());

        return new UnifiedInboxResult
        {
            Messages = messages,
            TotalCount = totalCount,
            UnreadCount = unreadCount,
            PageNumber = pageNumber,
            PageSize = pageSize,
            HasNextPage = skip + pageSize < totalCount,
            MessageCountByAccount = messageCountByAccount
        };
    }

    public async Task<AccountSummary> GetAccountSummaryAsync(int accountId)
    {
        var account = await _context.Accounts.FindAsync(accountId);
        if (account == null)
            throw new ArgumentException("Account not found", nameof(accountId));

        var totalMessages = await _context.Messages
            .CountAsync(m => m.AccountId == accountId && !m.IsDeleted);
        
        var unreadMessages = await _context.Messages
            .CountAsync(m => m.AccountId == accountId && !m.IsDeleted && !m.IsRead);
        
        var inboxMessages = await _context.Messages
            .Include(m => m.Folder)
            .CountAsync(m => m.AccountId == accountId && !m.IsDeleted && m.Folder!.Type == FolderType.Inbox);
        
        var totalSize = await _context.Messages
            .Where(m => m.AccountId == accountId && !m.IsDeleted)
            .SumAsync(m => (long)m.Size);

        // Check connection status
        var isConnected = false;
        var lastError = string.Empty;
        try
        {
            isConnected = await _imapService.TestConnectionAsync(account);
        }
        catch (Exception ex)
        {
            lastError = ex.Message;
        }

        return new AccountSummary
        {
            AccountId = account.Id,
            Name = account.Name,
            EmailAddress = account.EmailAddress,
            IsEnabled = account.IsEnabled,
            IsConnected = isConnected,
            LastSyncAt = account.LastSyncAt,
            TotalMessages = totalMessages,
            UnreadMessages = unreadMessages,
            InboxMessages = inboxMessages,
            TotalSize = totalSize,
            Status = isConnected ? "Connected" : "Disconnected",
            LastError = string.IsNullOrEmpty(lastError) ? null : lastError
        };
    }

    public async Task<IEnumerable<AccountSummary>> GetAllAccountSummariesAsync()
    {
        var accounts = await _context.Accounts.ToListAsync();
        var summaries = new List<AccountSummary>();

        foreach (var account in accounts)
        {
            try
            {
                var summary = await GetAccountSummaryAsync(account.Id);
                summaries.Add(summary);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get summary for account {AccountId}", account.Id);
                
                // Add a basic summary with error status
                summaries.Add(new AccountSummary
                {
                    AccountId = account.Id,
                    Name = account.Name,
                    EmailAddress = account.EmailAddress,
                    IsEnabled = account.IsEnabled,
                    IsConnected = false,
                    Status = "Error",
                    LastError = ex.Message
                });
            }
        }

        return summaries;
    }

    public async Task<CrossAccountSearchResult> SearchAcrossAccountsAsync(string query, int maxResults = 100)
    {
        var stopwatch = Stopwatch.StartNew();
        
        var messages = await _context.Messages
            .Include(m => m.Account)
            .Include(m => m.Folder)
            .Where(m => !m.IsDeleted && 
                       (m.Subject.Contains(query) || 
                        m.FromAddress.Contains(query) || 
                        m.FromName.Contains(query) ||
                        m.TextBody.Contains(query)))
            .OrderByDescending(m => m.DateReceived)
            .Take(maxResults)
            .ToListAsync();

        var resultsByAccount = messages
            .GroupBy(m => m.AccountId)
            .ToDictionary(g => g.Key, g => g.Count());

        stopwatch.Stop();

        return new CrossAccountSearchResult
        {
            Messages = messages,
            ResultsByAccount = resultsByAccount,
            TotalResults = messages.Count,
            SearchDuration = stopwatch.Elapsed
        };
    }

    public async Task<bool> MoveMessageBetweenAccountsAsync(int messageId, int targetAccountId, string targetFolderName)
    {
        // Note: This is a conceptual implementation
        // Moving messages between different email accounts isn't typically supported by IMAP
        // This would require downloading the message and uploading it to the target account
        
        var message = await _context.Messages
            .Include(m => m.Account)
            .Include(m => m.Folder)
            .FirstOrDefaultAsync(m => m.Id == messageId);

        if (message == null)
            return false;

        var targetAccount = await _context.Accounts.FindAsync(targetAccountId);
        if (targetAccount == null)
            return false;

        try
        {
            // This would require implementing SMTP sending to the target account
            // and then deleting from the source account
            _logger.LogWarning("Cross-account message moving is not yet implemented");
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to move message between accounts");
            return false;
        }
    }

    public async Task SyncAccountsInParallelAsync(IEnumerable<int> accountIds)
    {
        var tasks = accountIds.Select(async accountId =>
        {
            try
            {
                await _emailService.SyncAccountAsync(accountId);
                _logger.LogInformation("Successfully synced account {AccountId}", accountId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to sync account {AccountId}", accountId);
            }
        });

        await Task.WhenAll(tasks);
    }

    public async Task<AccountHealthStatus> CheckAccountHealthAsync(int accountId)
    {
        var account = await _context.Accounts.FindAsync(accountId);
        if (account == null)
            throw new ArgumentException("Account not found", nameof(accountId));

        var issues = new List<string>();
        var canConnect = false;
        var canAuthenticate = false;
        var canAccessInbox = false;

        try
        {
            // Test basic connection
            using var client = await _imapService.ConnectAsync(account);
            canConnect = true;
            canAuthenticate = client.IsAuthenticated;

            if (canAuthenticate)
            {
                // Test inbox access
                var folders = await _imapService.GetFoldersAsync(account);
                canAccessInbox = folders.Any(f => f.Type == FolderType.Inbox);
                
                if (!canAccessInbox)
                {
                    issues.Add("Cannot access inbox folder");
                }
            }
            else
            {
                issues.Add("Authentication failed");
            }
        }
        catch (Exception ex)
        {
            issues.Add($"Connection failed: {ex.Message}");
        }

        var isHealthy = canConnect && canAuthenticate && canAccessInbox && !issues.Any();

        return new AccountHealthStatus
        {
            AccountId = accountId,
            IsHealthy = isHealthy,
            CanConnect = canConnect,
            CanAuthenticate = canAuthenticate,
            CanAccessInbox = canAccessInbox,
            Issues = issues,
            LastChecked = DateTime.UtcNow
        };
    }

    public async Task<IEnumerable<EmailMessage>> GetRecentMessagesAcrossAccountsAsync(int hours = 24)
    {
        var cutoffTime = DateTime.UtcNow.AddHours(-hours);
        
        return await _context.Messages
            .Include(m => m.Account)
            .Include(m => m.Folder)
            .Where(m => !m.IsDeleted && m.DateReceived >= cutoffTime)
            .OrderByDescending(m => m.DateReceived)
            .Take(100)
            .ToListAsync();
    }
}
