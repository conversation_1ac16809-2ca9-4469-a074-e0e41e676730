<Window x:Class="EmailClient.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:local="clr-namespace:EmailClient.Converters"
        Title="Email Client" Height="800" Width="1200"
        WindowStartupLocation="CenterScreen">

    <Window.Resources>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        <local:NullToVisibilityConverter x:Key="NullToVisibilityConverter"/>
        <local:InvertedNullToVisibilityConverter x:Key="InvertedNullToVisibilityConverter"/>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Menu Bar -->
        <Menu Grid.Row="0">
            <MenuItem Header="_File">
                <MenuItem Header="_Add Account..." Command="{Binding AddAccountCommand}"/>
                <MenuItem Header="Account _Dashboard..." Command="{Binding ShowDashboardCommand}"/>
                <Separator/>
                <MenuItem Header="_Settings..." Command="{Binding SettingsCommand}"/>
                <Separator/>
                <MenuItem Header="E_xit" Command="{Binding ExitCommand}"/>
            </MenuItem>
            <MenuItem Header="_Email">
                <MenuItem Header="_Sync All" Command="{Binding SyncAllCommand}"/>
                <MenuItem Header="_Refresh" Command="{Binding RefreshCommand}"/>
                <Separator/>
                <MenuItem Header="_Search..." Command="{Binding SearchCommand}"/>
            </MenuItem>
            <MenuItem Header="_Help">
                <MenuItem Header="_About" Command="{Binding AboutCommand}"/>
            </MenuItem>
        </Menu>
        
        <!-- Main Content -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="250" MinWidth="200"/>
                <ColumnDefinition Width="5"/>
                <ColumnDefinition Width="400" MinWidth="300"/>
                <ColumnDefinition Width="5"/>
                <ColumnDefinition Width="*" MinWidth="300"/>
            </Grid.ColumnDefinitions>
            
            <!-- Left Panel - Accounts and Folders -->
            <Border Grid.Column="0" BorderBrush="LightGray" BorderThickness="0,0,1,0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <!-- Toolbar -->
                    <ToolBar Grid.Row="0">
                        <Button Content="Add Account" Command="{Binding AddAccountCommand}" ToolTip="Add new email account"/>
                        <Button Content="Sync" Command="{Binding SyncAllCommand}" ToolTip="Sync all accounts"/>
                    </ToolBar>
                    
                    <!-- Folder Tree -->
                    <TreeView Grid.Row="1" ItemsSource="{Binding FolderTree}" 
                              SelectedItemChanged="FolderTree_SelectedItemChanged">
                        <TreeView.ItemTemplate>
                            <HierarchicalDataTemplate ItemsSource="{Binding Children}">
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="{Binding Name}" FontWeight="{Binding FontWeight}"/>
                                    <TextBlock Text="{Binding UnreadCountDisplay}" 
                                               Foreground="Blue" 
                                               Margin="5,0,0,0"
                                               Visibility="{Binding HasUnread, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                                </StackPanel>
                            </HierarchicalDataTemplate>
                        </TreeView.ItemTemplate>
                    </TreeView>
                </Grid>
            </Border>
            
            <!-- Splitter -->
            <GridSplitter Grid.Column="1" HorizontalAlignment="Stretch" Background="LightGray"/>
            
            <!-- Middle Panel - Email List -->
            <Border Grid.Column="2" BorderBrush="LightGray" BorderThickness="0,0,1,0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <!-- Search Box -->
                    <Border Grid.Row="0" Padding="5" BorderBrush="LightGray" BorderThickness="0,0,0,1">
                        <TextBox x:Name="SearchBox"
                                 Text="{Binding SearchQuery, UpdateSourceTrigger=PropertyChanged}"
                                 KeyDown="SearchBox_KeyDown">
                            <TextBox.Style>
                                <Style TargetType="TextBox">
                                    <Setter Property="Foreground" Value="Black"/>
                                    <Style.Triggers>
                                        <Trigger Property="Text" Value="">
                                            <Setter Property="Background">
                                                <Setter.Value>
                                                    <VisualBrush AlignmentX="Left" AlignmentY="Center" Stretch="None">
                                                        <VisualBrush.Visual>
                                                            <Label Content="Search emails..." Foreground="LightGray"/>
                                                        </VisualBrush.Visual>
                                                    </VisualBrush>
                                                </Setter.Value>
                                            </Setter>
                                        </Trigger>
                                    </Style.Triggers>
                                </Style>
                            </TextBox.Style>
                        </TextBox>
                    </Border>
                    
                    <!-- Email List -->
                    <ListView Grid.Row="1" 
                              ItemsSource="{Binding EmailMessages}" 
                              SelectedItem="{Binding SelectedMessage}"
                              SelectionMode="Single">
                        <ListView.View>
                            <GridView>
                                <GridViewColumn Header="From" Width="150" DisplayMemberBinding="{Binding FromDisplay}"/>
                                <GridViewColumn Header="Subject" Width="200" DisplayMemberBinding="{Binding Subject}"/>
                                <GridViewColumn Header="Date" Width="100" DisplayMemberBinding="{Binding DateDisplay}"/>
                            </GridView>
                        </ListView.View>
                        <ListView.ItemContainerStyle>
                            <Style TargetType="ListViewItem">
                                <Setter Property="FontWeight" Value="{Binding FontWeight}"/>
                                <Style.Triggers>
                                    <DataTrigger Binding="{Binding IsRead}" Value="False">
                                        <Setter Property="FontWeight" Value="Bold"/>
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </ListView.ItemContainerStyle>
                    </ListView>
                </Grid>
            </Border>
            
            <!-- Splitter -->
            <GridSplitter Grid.Column="3" HorizontalAlignment="Stretch" Background="LightGray"/>
            
            <!-- Right Panel - Email Detail -->
            <Border Grid.Column="4">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <!-- Email Header -->
                    <Border Grid.Row="0" Padding="10" BorderBrush="LightGray" BorderThickness="0,0,0,1"
                            Visibility="{Binding SelectedMessage, Converter={StaticResource NullToVisibilityConverter}}">
                        <StackPanel>
                            <TextBlock Text="{Binding SelectedMessage.Subject}" FontSize="16" FontWeight="Bold" TextWrapping="Wrap"/>
                            <TextBlock Text="{Binding SelectedMessage.FromDisplay}" Margin="0,5,0,0"/>
                            <TextBlock Text="{Binding SelectedMessage.DateDisplay}" Margin="0,2,0,0" Foreground="Gray"/>
                            <StackPanel Orientation="Horizontal" Margin="0,5,0,0">
                                <Button Content="Reply" Command="{Binding ReplyCommand}" Margin="0,0,5,0"/>
                                <Button Content="Forward" Command="{Binding ForwardCommand}" Margin="0,0,5,0"/>
                                <Button Content="Delete" Command="{Binding DeleteCommand}" Margin="0,0,5,0"/>
                                <Button Content="{Binding MarkReadButtonText}" Command="{Binding MarkReadCommand}"/>
                            </StackPanel>
                        </StackPanel>
                    </Border>
                    
                    <!-- Email Body -->
                    <WebBrowser Grid.Row="1"
                                x:Name="EmailBodyBrowser"
                                Visibility="{Binding SelectedMessage, Converter={StaticResource NullToVisibilityConverter}}"/>

                    <!-- No Selection Message -->
                    <TextBlock Grid.Row="1"
                               Text="Select an email to view its content"
                               HorizontalAlignment="Center"
                               VerticalAlignment="Center"
                               Foreground="Gray"
                               FontSize="14"
                               Visibility="{Binding SelectedMessage, Converter={StaticResource InvertedNullToVisibilityConverter}}"/>
                </Grid>
            </Border>
        </Grid>
        
        <!-- Status Bar -->
        <StatusBar Grid.Row="2">
            <StatusBarItem>
                <TextBlock Text="{Binding StatusMessage}"/>
            </StatusBarItem>
            <StatusBarItem HorizontalAlignment="Right">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="{Binding TotalEmailCount}" Margin="0,0,5,0"/>
                    <TextBlock Text="emails"/>
                </StackPanel>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>
