<Window x:Class="EmailClient.Views.AccountDashboardWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Account Dashboard" Height="600" Width="900"
        WindowStartupLocation="CenterOwner">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <TextBlock Grid.Row="0" Text="Account Dashboard" FontSize="18" FontWeight="Bold" Margin="0,0,0,20"/>
        
        <!-- Summary Cards -->
        <Grid Grid.Row="1" Margin="0,0,0,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            
            <!-- Total Accounts Card -->
            <Border Grid.Column="0" Background="LightBlue" CornerRadius="5" Padding="15" Margin="0,0,10,0">
                <StackPanel>
                    <TextBlock Text="Total Accounts" FontWeight="Bold" FontSize="12"/>
                    <TextBlock Text="{Binding TotalAccounts}" FontSize="24" FontWeight="Bold"/>
                    <TextBlock Text="{Binding ConnectedAccounts, StringFormat=\{0\} connected}" FontSize="10" Foreground="DarkBlue"/>
                </StackPanel>
            </Border>
            
            <!-- Unread Messages Card -->
            <Border Grid.Column="1" Background="LightCoral" CornerRadius="5" Padding="15" Margin="0,0,10,0">
                <StackPanel>
                    <TextBlock Text="Unread Messages" FontWeight="Bold" FontSize="12"/>
                    <TextBlock Text="{Binding TotalUnreadMessages}" FontSize="24" FontWeight="Bold"/>
                    <TextBlock Text="across all accounts" FontSize="10" Foreground="DarkRed"/>
                </StackPanel>
            </Border>
            
            <!-- Storage Used Card -->
            <Border Grid.Column="2" Background="LightGreen" CornerRadius="5" Padding="15" Margin="0,0,10,0">
                <StackPanel>
                    <TextBlock Text="Storage Used" FontWeight="Bold" FontSize="12"/>
                    <TextBlock Text="{Binding TotalStorageDisplay}" FontSize="24" FontWeight="Bold"/>
                    <TextBlock Text="total size" FontSize="10" Foreground="DarkGreen"/>
                </StackPanel>
            </Border>
            
            <!-- Status Card -->
            <Border Grid.Column="3" Background="LightYellow" CornerRadius="5" Padding="15">
                <StackPanel>
                    <TextBlock Text="Status" FontWeight="Bold" FontSize="12"/>
                    <TextBlock Text="{Binding StatusMessage}" FontSize="12" FontWeight="Bold" TextWrapping="Wrap"/>
                    <ProgressBar IsIndeterminate="{Binding IsLoading}" Height="4" Margin="0,5,0,0"/>
                </StackPanel>
            </Border>
        </Grid>
        
        <!-- Account List -->
        <Grid Grid.Row="2">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>
            
            <!-- Toolbar -->
            <ToolBar Grid.Row="0" Margin="0,0,0,10">
                <Button Content="Refresh All" Command="{Binding RefreshSummariesCommand}"/>
                <Separator/>
                <Button Content="Sync Selected" Command="{Binding SyncSelectedAccountCommand}" 
                        IsEnabled="{Binding SelectedAccount, Converter={StaticResource NullToVisibilityConverter}}"/>
                <Button Content="Sync All" Command="{Binding SyncAllAccountsCommand}"/>
                <Separator/>
                <Button Content="Check Health" Command="{Binding CheckAccountHealthCommand}" 
                        IsEnabled="{Binding SelectedAccount, Converter={StaticResource NullToVisibilityConverter}}"/>
            </ToolBar>
            
            <!-- Account DataGrid -->
            <DataGrid Grid.Row="1" 
                      ItemsSource="{Binding AccountSummaries}" 
                      SelectedItem="{Binding SelectedAccount}"
                      AutoGenerateColumns="False"
                      CanUserAddRows="False"
                      CanUserDeleteRows="False"
                      GridLinesVisibility="Horizontal"
                      HeadersVisibility="Column">
                <DataGrid.Columns>
                    <DataGridTextColumn Header="Name" Binding="{Binding Name}" Width="150"/>
                    <DataGridTextColumn Header="Email" Binding="{Binding EmailAddress}" Width="200"/>
                    <DataGridTextColumn Header="Status" Binding="{Binding Status}" Width="100">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Style.Triggers>
                                    <Trigger Property="Text" Value="Connected">
                                        <Setter Property="Foreground" Value="Green"/>
                                    </Trigger>
                                    <Trigger Property="Text" Value="Disconnected">
                                        <Setter Property="Foreground" Value="Red"/>
                                    </Trigger>
                                    <Trigger Property="Text" Value="Error">
                                        <Setter Property="Foreground" Value="DarkRed"/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    <DataGridTextColumn Header="Total Messages" Binding="{Binding TotalMessages}" Width="120"/>
                    <DataGridTextColumn Header="Unread" Binding="{Binding UnreadDisplay}" Width="100"/>
                    <DataGridTextColumn Header="Inbox" Binding="{Binding InboxMessages}" Width="80"/>
                    <DataGridTextColumn Header="Size" Binding="{Binding TotalSizeDisplay}" Width="100"/>
                    <DataGridTextColumn Header="Last Sync" Binding="{Binding LastSyncDisplay}" Width="120"/>
                    <DataGridCheckBoxColumn Header="Enabled" Binding="{Binding IsEnabled}" Width="80"/>
                </DataGrid.Columns>
                <DataGrid.RowStyle>
                    <Style TargetType="DataGridRow">
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding IsConnected}" Value="False">
                                <Setter Property="Background" Value="LightPink"/>
                            </DataTrigger>
                            <DataTrigger Binding="{Binding IsEnabled}" Value="False">
                                <Setter Property="Foreground" Value="Gray"/>
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </DataGrid.RowStyle>
            </DataGrid>
        </Grid>
        
        <!-- Details Panel -->
        <Border Grid.Row="3" BorderBrush="LightGray" BorderThickness="1" Padding="10" Margin="0,20,0,0"
                Visibility="{Binding SelectedAccount, Converter={StaticResource NullToVisibilityConverter}}">
            <StackPanel>
                <TextBlock Text="Account Details" FontWeight="Bold" Margin="0,0,0,10"/>
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    
                    <TextBlock Grid.Row="0" Grid.Column="0" Text="Health Status:" FontWeight="Bold" Margin="0,0,10,5"/>
                    <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding SelectedAccount.HealthStatus}" Margin="0,0,20,5"/>
                    
                    <TextBlock Grid.Row="0" Grid.Column="2" Text="Connection:" FontWeight="Bold" Margin="0,0,10,5"/>
                    <TextBlock Grid.Row="0" Grid.Column="3" Text="{Binding SelectedAccount.Status}" Margin="0,0,0,5"/>
                    
                    <TextBlock Grid.Row="1" Grid.Column="0" Text="Last Error:" FontWeight="Bold" Margin="0,0,10,5"/>
                    <TextBlock Grid.Row="1" Grid.Column="1" Grid.ColumnSpan="3" Text="{Binding SelectedAccount.LastError}" 
                               Foreground="Red" TextWrapping="Wrap" Margin="0,0,0,5"
                               Visibility="{Binding SelectedAccount.LastError, Converter={StaticResource NullToVisibilityConverter}}"/>
                    
                    <TextBlock Grid.Row="2" Grid.Column="0" Text="Statistics:" FontWeight="Bold" Margin="0,0,10,0"/>
                    <TextBlock Grid.Row="2" Grid.Column="1" Grid.ColumnSpan="3" Margin="0,0,0,0">
                        <TextBlock.Text>
                            <MultiBinding StringFormat="{}{0} total messages, {1} unread, {2} in inbox, {3} storage used">
                                <Binding Path="SelectedAccount.TotalMessages"/>
                                <Binding Path="SelectedAccount.UnreadMessages"/>
                                <Binding Path="SelectedAccount.InboxMessages"/>
                                <Binding Path="SelectedAccount.TotalSizeDisplay"/>
                            </MultiBinding>
                        </TextBlock.Text>
                    </TextBlock>
                </Grid>
            </StackPanel>
        </Border>
    </Grid>
</Window>
