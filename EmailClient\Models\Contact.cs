using System.ComponentModel.DataAnnotations;

namespace EmailClient.Models;

public class Contact
{
    public int Id { get; set; }
    
    [Required]
    public string GoogleId { get; set; } = string.Empty; // Google People API resource name
    
    public string DisplayName { get; set; } = string.Empty;
    
    public string GivenName { get; set; } = string.Empty;
    
    public string FamilyName { get; set; } = string.Empty;
    
    public string MiddleName { get; set; } = string.Empty;
    
    public string EmailAddresses { get; set; } = string.Empty; // JSON array of email addresses
    
    public string PhoneNumbers { get; set; } = string.Empty; // JSON array of phone numbers
    
    public string Addresses { get; set; } = string.Empty; // JSON array of addresses
    
    public string Organizations { get; set; } = string.Empty; // JSON array of organizations
    
    public string PhotoUrl { get; set; } = string.Empty;
    
    public string Notes { get; set; } = string.Empty;
    
    public DateTime? Birthday { get; set; }
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime LastSyncAt { get; set; } = DateTime.UtcNow;
    
    public string ETag { get; set; } = string.Empty; // For optimistic concurrency
    
    public bool IsDeleted { get; set; } = false;
}

// Helper classes for JSON serialization
public class ContactEmail
{
    public string Value { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty; // home, work, other
    public bool Primary { get; set; } = false;
}

public class ContactPhone
{
    public string Value { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty; // home, work, mobile, other
    public bool Primary { get; set; } = false;
}

public class ContactAddress
{
    public string FormattedValue { get; set; } = string.Empty;
    public string StreetAddress { get; set; } = string.Empty;
    public string City { get; set; } = string.Empty;
    public string Region { get; set; } = string.Empty;
    public string PostalCode { get; set; } = string.Empty;
    public string Country { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty; // home, work, other
}

public class ContactOrganization
{
    public string Name { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Department { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty; // work, school, other
}
