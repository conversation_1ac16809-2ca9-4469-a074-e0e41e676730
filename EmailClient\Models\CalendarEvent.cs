using System.ComponentModel.DataAnnotations;

namespace EmailClient.Models;

public class CalendarEvent
{
    public int Id { get; set; }
    
    [Required]
    public string GoogleId { get; set; } = string.Empty; // Google Calendar event ID
    
    [Required]
    public string CalendarId { get; set; } = string.Empty; // Google Calendar ID
    
    public string Summary { get; set; } = string.Empty; // Event title
    
    public string Description { get; set; } = string.Empty;
    
    public string Location { get; set; } = string.Empty;
    
    public DateTime? StartDateTime { get; set; }
    
    public DateTime? EndDateTime { get; set; }
    
    public bool IsAllDay { get; set; } = false;
    
    public string TimeZone { get; set; } = string.Empty;
    
    public string Status { get; set; } = string.Empty; // confirmed, tentative, cancelled
    
    public string Visibility { get; set; } = string.Empty; // default, public, private, confidential
    
    public string Attendees { get; set; } = string.Empty; // JSON array of attendees
    
    public string Organizer { get; set; } = string.Empty; // JSON object for organizer
    
    public string Creator { get; set; } = string.Empty; // JSON object for creator
    
    public string RecurrenceRule { get; set; } = string.Empty; // RRULE for recurring events
    
    public string RecurringEventId { get; set; } = string.Empty; // For recurring event instances
    
    public string HtmlLink { get; set; } = string.Empty; // Link to event in Google Calendar
    
    public string HangoutLink { get; set; } = string.Empty; // Google Meet link
    
    public string ConferenceData { get; set; } = string.Empty; // JSON for conference/meeting data
    
    public string Reminders { get; set; } = string.Empty; // JSON array of reminders
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime LastSyncAt { get; set; } = DateTime.UtcNow;
    
    public string ETag { get; set; } = string.Empty; // For optimistic concurrency
    
    public bool IsDeleted { get; set; } = false;
}

// Helper classes for JSON serialization
public class EventAttendee
{
    public string Email { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string ResponseStatus { get; set; } = string.Empty; // needsAction, declined, tentative, accepted
    public bool Optional { get; set; } = false;
    public bool Organizer { get; set; } = false;
    public bool Resource { get; set; } = false;
}

public class EventOrganizer
{
    public string Email { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public bool Self { get; set; } = false;
}

public class EventReminder
{
    public string Method { get; set; } = string.Empty; // email, popup
    public int Minutes { get; set; } = 0;
}

public class ConferenceInfo
{
    public string Type { get; set; } = string.Empty; // hangoutsMeet, addOn
    public string Uri { get; set; } = string.Empty;
    public string ConferenceId { get; set; } = string.Empty;
    public string Signature { get; set; } = string.Empty;
}
