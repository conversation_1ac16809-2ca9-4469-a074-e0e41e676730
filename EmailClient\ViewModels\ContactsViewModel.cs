using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using EmailClient.Models;
using EmailClient.Services;
using Microsoft.Extensions.Logging;
using System.Collections.ObjectModel;
using System.Text.Json;
using System.Windows;

namespace EmailClient.ViewModels;

public partial class ContactsViewModel : ViewModelBase
{
    private readonly IGoogleContactsService _contactsService;
    private readonly ILogger<ContactsViewModel> _logger;

    [ObservableProperty]
    private ObservableCollection<ContactViewModel> _contacts = new();

    [ObservableProperty]
    private ContactViewModel? _selectedContact;

    [ObservableProperty]
    private string _searchQuery = string.Empty;

    [ObservableProperty]
    private string _statusMessage = "Ready";

    [ObservableProperty]
    private int _contactsCount = 0;

    public string ContactsCountDisplay => $"{ContactsCount} contacts";

    public ContactsViewModel(IGoogleContactsService contactsService, ILogger<ContactsViewModel> logger)
    {
        _contactsService = contactsService;
        _logger = logger;

        _ = LoadContactsAsync();
    }

    [RelayCommand]
    private async Task SyncContacts()
    {
        try
        {
            StatusMessage = "Syncing contacts...";
            var count = await _contactsService.SyncContactsAsync(fullSync: true);
            StatusMessage = $"Synced {count} contacts";
            
            await LoadContactsAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to sync contacts");
            StatusMessage = "Failed to sync contacts";
            MessageBox.Show("Failed to sync contacts", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    [RelayCommand]
    private async Task Search()
    {
        try
        {
            if (string.IsNullOrWhiteSpace(SearchQuery))
            {
                await LoadContactsAsync();
                return;
            }

            StatusMessage = "Searching...";
            var searchResults = await _contactsService.SearchContactsAsync(SearchQuery);
            
            Contacts.Clear();
            foreach (var contact in searchResults)
            {
                Contacts.Add(new ContactViewModel(contact));
            }

            ContactsCount = Contacts.Count;
            StatusMessage = $"Found {ContactsCount} contacts";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to search contacts");
            StatusMessage = "Search failed";
        }
    }

    [RelayCommand]
    private async Task ClearSearch()
    {
        SearchQuery = string.Empty;
        await LoadContactsAsync();
    }

    private async Task LoadContactsAsync()
    {
        try
        {
            StatusMessage = "Loading contacts...";
            var contacts = await _contactsService.GetContactsAsync();
            
            Contacts.Clear();
            foreach (var contact in contacts)
            {
                Contacts.Add(new ContactViewModel(contact));
            }

            ContactsCount = Contacts.Count;
            StatusMessage = "Ready";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load contacts");
            StatusMessage = "Failed to load contacts";
        }
    }

    partial void OnSearchQueryChanged(string value)
    {
        if (string.IsNullOrWhiteSpace(value))
        {
            _ = LoadContactsAsync();
        }
    }
}

public class ContactViewModel
{
    private readonly Contact _contact;

    public ContactViewModel(Contact contact)
    {
        _contact = contact;
    }

    public int Id => _contact.Id;
    public string DisplayName => _contact.DisplayName;
    public string GivenName => _contact.GivenName;
    public string FamilyName => _contact.FamilyName;
    public string PhotoUrl => _contact.PhotoUrl;
    public DateTime LastSyncAt => _contact.LastSyncAt;

    public string PrimaryEmail
    {
        get
        {
            try
            {
                if (string.IsNullOrEmpty(_contact.EmailAddresses))
                    return string.Empty;

                var emails = JsonSerializer.Deserialize<ContactEmail[]>(_contact.EmailAddresses);
                var primary = emails?.FirstOrDefault(e => e.Primary) ?? emails?.FirstOrDefault();
                return primary?.Value ?? string.Empty;
            }
            catch
            {
                return string.Empty;
            }
        }
    }

    public string PrimaryPhone
    {
        get
        {
            try
            {
                if (string.IsNullOrEmpty(_contact.PhoneNumbers))
                    return string.Empty;

                var phones = JsonSerializer.Deserialize<ContactPhone[]>(_contact.PhoneNumbers);
                var primary = phones?.FirstOrDefault(p => p.Primary) ?? phones?.FirstOrDefault();
                return primary?.Value ?? string.Empty;
            }
            catch
            {
                return string.Empty;
            }
        }
    }

    public string PrimaryOrganization
    {
        get
        {
            try
            {
                if (string.IsNullOrEmpty(_contact.Organizations))
                    return string.Empty;

                var orgs = JsonSerializer.Deserialize<ContactOrganization[]>(_contact.Organizations);
                var primary = orgs?.FirstOrDefault();
                return primary?.Name ?? string.Empty;
            }
            catch
            {
                return string.Empty;
            }
        }
    }
}
